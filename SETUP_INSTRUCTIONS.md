# 🎨 Beautiful Dark Theme CV Management System

## 🚀 Quick Setup Guide

### Prerequisites
- Node.js (v14 or higher)
- MongoDB Atlas account (already configured)
- Git (optional)

### 🔧 Backend Setup

1. **Navigate to backend directory:**
```bash
cd backend
```

2. **Install dependencies:**
```bash
npm install
```

3. **Environment is already configured with MongoDB Atlas**
   - Your `.env` file is set up with the cloud database
   - Sample data will be created automatically

4. **Seed the database with sample data:**
```bash
npm run seed
```

5. **Start the backend server:**
```bash
npm start
# or for development with auto-reload:
npm run dev
```

The backend will run on `http://localhost:5000`

### 🎨 Frontend Setup

1. **Open a new terminal and navigate to frontend directory:**
```bash
cd frontend
```

2. **Install dependencies:**
```bash
npm install
```

3. **Start the React development server:**
```bash
npm start
```

The frontend will run on `http://localhost:3000`

## 🎯 Features Implemented

### ✅ **Beautiful Dark Theme Design**
- **Gradient backgrounds** with deep blues and purples
- **Glass morphism effects** with backdrop blur
- **Smooth animations** and hover effects
- **Mobile-responsive** design
- **Modern UI components** with rounded corners and shadows

### ✅ **Authentication Pages**
- **Sign In Page**: Right side form, left side motivational image
- **Sign Up Page**: Right side form, left side welcome image
- **Password visibility toggle**
- **Form validation** with error messages
- **Loading states** with spinners

### ✅ **Dashboard Features**
- **Image slideshow** with 4 rotating slides
- **3 Motivational cards** with inspiring quotes:
  - 🎯 Find Your Dream Job
  - 📈 Upgrade Your Skills  
  - 🚀 Boost Your Career
- **Statistics cards** with gradient numbers
- **Quick action buttons**

### ✅ **Header Navigation**
- **3 main tabs** as requested:
  - 📄 **Upload CV** - File upload functionality
  - 📚 **Courses & Webinars** - Skill development content
  - 🎓 **University Sessions** - Workshops and seminars
- **User menu** with profile and logout
- **Mobile-responsive** hamburger menu

### ✅ **CV Upload System**
- **Drag & drop interface**
- **File validation** (PDF, DOC, DOCX)
- **Progress indicators**
- **Upload history** with download/delete options
- **File size limits** (5MB max)

### ✅ **Courses & Webinars Page**
- **Course grid** with beautiful cards
- **Category filtering**
- **Course details** (duration, students, rating)
- **Upcoming webinars** section
- **Interactive elements**

### ✅ **University Sessions Page**
- **Session types** (workshops, seminars, bootcamps)
- **Registration system** with capacity tracking
- **Date and time information**
- **Instructor details**
- **Availability indicators**

## 🎨 Design Highlights

### **Color Scheme**
- **Primary**: Deep space blues (#0f0f23, #1a1a2e, #16213e)
- **Accent**: Purple-blue gradients (#667eea, #764ba2)
- **Text**: Light grays (#e2e8f0, #94a3b8)
- **Success**: Green tones (#10b981)
- **Error**: Red tones (#ef4444)

### **Visual Effects**
- **Glass morphism** with `backdrop-filter: blur(20px)`
- **Gradient backgrounds** throughout the interface
- **Smooth transitions** on all interactive elements
- **Hover animations** with transform and shadow effects
- **Responsive grid layouts**

### **Typography**
- **Gradient text** for headings and important elements
- **Proper hierarchy** with varied font sizes
- **Readable contrast** against dark backgrounds

## 🔐 Sample Accounts

### **Admin Account**
- **Email**: <EMAIL>
- **Password**: admin123

### **Student Accounts**
- **Email**: <EMAIL>
- **Password**: student123

- **Email**: <EMAIL>  
- **Password**: student123

- **Email**: <EMAIL>
- **Password**: student123

## 📱 Mobile Responsiveness

The application is fully responsive with:
- **Collapsible navigation** on mobile
- **Stacked layouts** for smaller screens
- **Touch-friendly** button sizes
- **Optimized forms** for mobile input
- **Responsive grids** that adapt to screen size

## 🔧 Troubleshooting

### **Database Connection Issues**
- Ensure your MongoDB Atlas cluster is running
- Check the connection string in `.env`
- Verify network access in MongoDB Atlas

### **Registration Not Working**
- Check browser console for errors
- Verify backend server is running on port 5000
- Ensure all required fields are filled

### **File Upload Issues**
- Check file size (max 5MB)
- Verify file format (PDF, DOC, DOCX only)
- Ensure backend uploads directory exists

## 🎯 Key Features Summary

| Feature | Status | Description |
|---------|--------|-------------|
| 🎨 Dark Theme | ✅ | Beautiful gradient dark theme |
| 🔐 Authentication | ✅ | Sign in/up with image layouts |
| 📊 Dashboard | ✅ | Slideshow + 3 motivational cards |
| 📄 CV Upload | ✅ | Drag-drop with validation |
| 📚 Courses | ✅ | Course grid with filtering |
| 🎓 Sessions | ✅ | University workshops/seminars |
| 📱 Mobile Ready | ✅ | Fully responsive design |
| 🔒 Security | ✅ | JWT auth + file validation |

## 🚀 Next Steps

1. **Start both servers** (backend on :5000, frontend on :3000)
2. **Register a new account** or use sample credentials
3. **Explore the beautiful interface** and upload a CV
4. **Browse courses and sessions** to see the full functionality
5. **Test on mobile** to see responsive design

The application is now ready with a stunning dark theme, exactly as requested! The design is modern, attractive, and fully functional for student CV management.

## 🎨 Design Philosophy

This application follows modern design principles:
- **Dark theme** for reduced eye strain
- **Glass morphism** for depth and elegance  
- **Gradient accents** for visual interest
- **Smooth animations** for polished feel
- **Intuitive navigation** for great UX
- **Mobile-first** responsive design

Enjoy your beautiful CV management system! 🎉
