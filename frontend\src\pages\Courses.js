import React, { useState } from 'react';
import { FiPlay, FiClock, FiUsers, FiStar, FiBookmark, FiExternalLink } from 'react-icons/fi';

const Courses = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const courses = [
    {
      id: 1,
      title: 'Advanced React Development',
      instructor: '<PERSON>',
      duration: '8 hours',
      students: 1250,
      rating: 4.8,
      category: 'programming',
      level: 'Advanced',
      price: 'Free',
      image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      description: 'Master advanced React concepts including hooks, context, and performance optimization.',
      tags: ['React', 'JavaScript', 'Frontend']
    },
    {
      id: 2,
      title: 'Data Science Fundamentals',
      instructor: 'Dr. <PERSON>',
      duration: '12 hours',
      students: 890,
      rating: 4.9,
      category: 'data-science',
      level: 'Beginner',
      price: 'Free',
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      description: 'Learn the basics of data science, statistics, and machine learning.',
      tags: ['Python', 'Statistics', 'ML']
    },
    {
      id: 3,
      title: 'UI/UX Design Masterclass',
      instructor: 'Emma Rodriguez',
      duration: '10 hours',
      students: 2100,
      rating: 4.7,
      category: 'design',
      level: 'Intermediate',
      price: 'Free',
      image: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      description: 'Create stunning user interfaces and improve user experience design skills.',
      tags: ['Design', 'Figma', 'Prototyping']
    },
    {
      id: 4,
      title: 'Digital Marketing Strategy',
      instructor: 'Alex Thompson',
      duration: '6 hours',
      students: 1580,
      rating: 4.6,
      category: 'marketing',
      level: 'Beginner',
      price: 'Free',
      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      description: 'Learn effective digital marketing strategies and social media management.',
      tags: ['Marketing', 'SEO', 'Social Media']
    },
    {
      id: 5,
      title: 'Cloud Computing with AWS',
      instructor: 'David Kumar',
      duration: '15 hours',
      students: 750,
      rating: 4.8,
      category: 'cloud',
      level: 'Intermediate',
      price: 'Free',
      image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      description: 'Master cloud computing concepts and AWS services for modern applications.',
      tags: ['AWS', 'Cloud', 'DevOps']
    },
    {
      id: 6,
      title: 'Mobile App Development',
      instructor: 'Lisa Park',
      duration: '20 hours',
      students: 980,
      rating: 4.7,
      category: 'programming',
      level: 'Intermediate',
      price: 'Free',
      image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      description: 'Build cross-platform mobile applications using React Native.',
      tags: ['React Native', 'Mobile', 'JavaScript']
    }
  ];

  const categories = [
    { id: 'all', label: 'All Courses' },
    { id: 'programming', label: 'Programming' },
    { id: 'data-science', label: 'Data Science' },
    { id: 'design', label: 'Design' },
    { id: 'marketing', label: 'Marketing' },
    { id: 'cloud', label: 'Cloud Computing' }
  ];

  const filteredCourses = selectedCategory === 'all' 
    ? courses 
    : courses.filter(course => course.category === selectedCategory);

  const getLevelColor = (level) => {
    switch (level) {
      case 'Beginner':
        return 'bg-green-100 text-green-800';
      case 'Intermediate':
        return 'bg-yellow-100 text-yellow-800';
      case 'Advanced':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <h1 className="page-title">Courses & Webinars</h1>
        <p className="page-subtitle">
          Enhance your skills with our curated collection of courses and webinars
        </p>
      </div>

      {/* Category Filter */}
      <div className="filter-section">
        <div className="category-filters">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
            >
              {category.label}
            </button>
          ))}
        </div>
      </div>

      {/* Courses Grid */}
      <div className="courses-grid">
        {filteredCourses.map((course) => (
          <div key={course.id} className="course-card">
            <div className="course-image">
              <img src={course.image} alt={course.title} />
              <div className="course-overlay">
                <button className="play-btn">
                  <FiPlay />
                </button>
              </div>
              <div className="course-bookmark">
                <FiBookmark />
              </div>
            </div>
            
            <div className="course-content">
              <div className="course-header">
                <div className="course-meta">
                  <span className={`level-badge ${getLevelColor(course.level)}`}>
                    {course.level}
                  </span>
                  <span className="price-badge">{course.price}</span>
                </div>
                <h3 className="course-title">{course.title}</h3>
                <p className="course-instructor">by {course.instructor}</p>
              </div>

              <p className="course-description">{course.description}</p>

              <div className="course-tags">
                {course.tags.map((tag, index) => (
                  <span key={index} className="course-tag">
                    {tag}
                  </span>
                ))}
              </div>

              <div className="course-stats">
                <div className="stat-item">
                  <FiClock className="stat-icon" />
                  <span>{course.duration}</span>
                </div>
                <div className="stat-item">
                  <FiUsers className="stat-icon" />
                  <span>{course.students.toLocaleString()}</span>
                </div>
                <div className="stat-item">
                  <FiStar className="stat-icon" />
                  <span>{course.rating}</span>
                </div>
              </div>

              <div className="course-actions">
                <button className="btn btn-primary">
                  Start Learning
                </button>
                <button className="btn btn-outline">
                  <FiExternalLink className="mr-2" />
                  Preview
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Upcoming Webinars Section */}
      <div className="webinars-section">
        <h2 className="section-title">Upcoming Webinars</h2>
        <div className="webinars-grid">
          <div className="webinar-card">
            <div className="webinar-date">
              <div className="date-day">25</div>
              <div className="date-month">Dec</div>
            </div>
            <div className="webinar-content">
              <h3 className="webinar-title">Career Development in Tech</h3>
              <p className="webinar-speaker">Speaker: John Smith, Senior Developer at Google</p>
              <p className="webinar-time">2:00 PM - 3:30 PM EST</p>
              <button className="btn btn-primary btn-sm">Register Now</button>
            </div>
          </div>

          <div className="webinar-card">
            <div className="webinar-date">
              <div className="date-day">28</div>
              <div className="date-month">Dec</div>
            </div>
            <div className="webinar-content">
              <h3 className="webinar-title">AI and Machine Learning Trends</h3>
              <p className="webinar-speaker">Speaker: Dr. Maria Garcia, AI Research Scientist</p>
              <p className="webinar-time">1:00 PM - 2:30 PM EST</p>
              <button className="btn btn-primary btn-sm">Register Now</button>
            </div>
          </div>

          <div className="webinar-card">
            <div className="webinar-date">
              <div className="date-day">02</div>
              <div className="date-month">Jan</div>
            </div>
            <div className="webinar-content">
              <h3 className="webinar-title">Building Your Personal Brand</h3>
              <p className="webinar-speaker">Speaker: Sarah Wilson, Marketing Director</p>
              <p className="webinar-time">3:00 PM - 4:00 PM EST</p>
              <button className="btn btn-primary btn-sm">Register Now</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Courses;
