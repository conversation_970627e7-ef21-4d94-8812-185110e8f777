{"ast": null, "code": "\"use strict\";\n\nexports.__esModule = true;\nexports.default = function (file, acceptedFiles) {\n  if (file && acceptedFiles) {\n    var acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n    if (acceptedFilesArray.length === 0) {\n      return true;\n    }\n    var fileName = file.name || '';\n    var mimeType = (file.type || '').toLowerCase();\n    var baseMimeType = mimeType.replace(/\\/.*$/, '');\n    return acceptedFilesArray.some(function (type) {\n      var validType = type.trim().toLowerCase();\n      if (validType.charAt(0) === '.') {\n        return fileName.toLowerCase().endsWith(validType);\n      } else if (validType.endsWith('/*')) {\n        // This is something like a image/* mime type\n        return baseMimeType === validType.replace(/\\/.*$/, '');\n      }\n      return mimeType === validType;\n    });\n  }\n  return true;\n};", "map": {"version": 3, "names": ["exports", "__esModule", "default", "file", "acceptedFiles", "acceptedFilesArray", "Array", "isArray", "split", "length", "fileName", "name", "mimeType", "type", "toLowerCase", "baseMimeType", "replace", "some", "validType", "trim", "char<PERSON>t", "endsWith"], "sources": ["C:/Users/<USER>/Documents/OutSource/CV/frontend/node_modules/attr-accept/dist/es/index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\n\nexports.default = function (file, acceptedFiles) {\n  if (file && acceptedFiles) {\n    var acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n\n    if (acceptedFilesArray.length === 0) {\n      return true;\n    }\n\n    var fileName = file.name || '';\n    var mimeType = (file.type || '').toLowerCase();\n    var baseMimeType = mimeType.replace(/\\/.*$/, '');\n    return acceptedFilesArray.some(function (type) {\n      var validType = type.trim().toLowerCase();\n\n      if (validType.charAt(0) === '.') {\n        return fileName.toLowerCase().endsWith(validType);\n      } else if (validType.endsWith('/*')) {\n        // This is something like a image/* mime type\n        return baseMimeType === validType.replace(/\\/.*$/, '');\n      }\n\n      return mimeType === validType;\n    });\n  }\n\n  return true;\n};"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzBD,OAAO,CAACE,OAAO,GAAG,UAAUC,IAAI,EAAEC,aAAa,EAAE;EAC/C,IAAID,IAAI,IAAIC,aAAa,EAAE;IACzB,IAAIC,kBAAkB,GAAGC,KAAK,CAACC,OAAO,CAACH,aAAa,CAAC,GAAGA,aAAa,GAAGA,aAAa,CAACI,KAAK,CAAC,GAAG,CAAC;IAEhG,IAAIH,kBAAkB,CAACI,MAAM,KAAK,CAAC,EAAE;MACnC,OAAO,IAAI;IACb;IAEA,IAAIC,QAAQ,GAAGP,IAAI,CAACQ,IAAI,IAAI,EAAE;IAC9B,IAAIC,QAAQ,GAAG,CAACT,IAAI,CAACU,IAAI,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC;IAC9C,IAAIC,YAAY,GAAGH,QAAQ,CAACI,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IAChD,OAAOX,kBAAkB,CAACY,IAAI,CAAC,UAAUJ,IAAI,EAAE;MAC7C,IAAIK,SAAS,GAAGL,IAAI,CAACM,IAAI,CAAC,CAAC,CAACL,WAAW,CAAC,CAAC;MAEzC,IAAII,SAAS,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC/B,OAAOV,QAAQ,CAACI,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACH,SAAS,CAAC;MACnD,CAAC,MAAM,IAAIA,SAAS,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAE;QACnC;QACA,OAAON,YAAY,KAAKG,SAAS,CAACF,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MACxD;MAEA,OAAOJ,QAAQ,KAAKM,SAAS;IAC/B,CAAC,CAAC;EACJ;EAEA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}