{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\OutSource\\\\CV\\\\frontend\\\\src\\\\components\\\\Footer.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"footer\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"\\xA9 \", currentYear, \" CV Management System. All rights reserved.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Footer", "currentYear", "Date", "getFullYear", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/OutSource/CV/frontend/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"footer\">\n      <div className=\"container\">\n        <p>&copy; {currentYear} CV Management System. All rights reserved.</p>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,oBACEJ,OAAA;IAAQK,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBN,OAAA;MAAKK,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBN,OAAA;QAAAM,QAAA,GAAG,OAAO,EAACJ,WAAW,EAAC,6CAA2C;MAAA;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACC,EAAA,GAVIV,MAAM;AAYZ,eAAeA,MAAM;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}