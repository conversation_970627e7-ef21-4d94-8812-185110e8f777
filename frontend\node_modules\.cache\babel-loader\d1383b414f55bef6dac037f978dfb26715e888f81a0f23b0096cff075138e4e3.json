{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\OutSource\\\\CV\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport LoadingSpinner from './LoadingSpinner';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 12\n    }, this);\n  }\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/signin\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"EmJkapf7qiLC5Br5eCoEq4veZes=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useAuth", "LoadingSpinner", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "user", "loading", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/OutSource/CV/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport LoadingSpinner from './LoadingSpinner';\n\nconst ProtectedRoute = ({ children }) => {\n  const { user, loading } = useAuth();\n\n  if (loading) {\n    return <LoadingSpinner />;\n  }\n\n  if (!user) {\n    return <Navigate to=\"/signin\" replace />;\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGR,OAAO,CAAC,CAAC;EAEnC,IAAIQ,OAAO,EAAE;IACX,oBAAOL,OAAA,CAACF,cAAc;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B;EAEA,IAAI,CAACL,IAAI,EAAE;IACT,oBAAOJ,OAAA,CAACJ,QAAQ;MAACc,EAAE,EAAC,SAAS;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1C;EAEA,OAAOP,QAAQ;AACjB,CAAC;AAACC,EAAA,CAZIF,cAAc;EAAA,QACQJ,OAAO;AAAA;AAAAe,EAAA,GAD7BX,cAAc;AAcpB,eAAeA,cAAc;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}