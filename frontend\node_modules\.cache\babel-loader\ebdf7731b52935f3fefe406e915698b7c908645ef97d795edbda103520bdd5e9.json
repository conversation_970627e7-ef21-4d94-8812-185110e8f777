{"ast": null, "code": "export const COMMON_MIME_TYPES = new Map([\n// https://github.com/guzzle/psr7/blob/2d9260799e713f1c475d3c5fdc3d6561ff7441b2/src/MimeType.php\n['1km', 'application/vnd.1000minds.decision-model+xml'], ['3dml', 'text/vnd.in3d.3dml'], ['3ds', 'image/x-3ds'], ['3g2', 'video/3gpp2'], ['3gp', 'video/3gp'], ['3gpp', 'video/3gpp'], ['3mf', 'model/3mf'], ['7z', 'application/x-7z-compressed'], ['7zip', 'application/x-7z-compressed'], ['123', 'application/vnd.lotus-1-2-3'], ['aab', 'application/x-authorware-bin'], ['aac', 'audio/x-acc'], ['aam', 'application/x-authorware-map'], ['aas', 'application/x-authorware-seg'], ['abw', 'application/x-abiword'], ['ac', 'application/vnd.nokia.n-gage.ac+xml'], ['ac3', 'audio/ac3'], ['acc', 'application/vnd.americandynamics.acc'], ['ace', 'application/x-ace-compressed'], ['acu', 'application/vnd.acucobol'], ['acutc', 'application/vnd.acucorp'], ['adp', 'audio/adpcm'], ['aep', 'application/vnd.audiograph'], ['afm', 'application/x-font-type1'], ['afp', 'application/vnd.ibm.modcap'], ['ahead', 'application/vnd.ahead.space'], ['ai', 'application/pdf'], ['aif', 'audio/x-aiff'], ['aifc', 'audio/x-aiff'], ['aiff', 'audio/x-aiff'], ['air', 'application/vnd.adobe.air-application-installer-package+zip'], ['ait', 'application/vnd.dvb.ait'], ['ami', 'application/vnd.amiga.ami'], ['amr', 'audio/amr'], ['apk', 'application/vnd.android.package-archive'], ['apng', 'image/apng'], ['appcache', 'text/cache-manifest'], ['application', 'application/x-ms-application'], ['apr', 'application/vnd.lotus-approach'], ['arc', 'application/x-freearc'], ['arj', 'application/x-arj'], ['asc', 'application/pgp-signature'], ['asf', 'video/x-ms-asf'], ['asm', 'text/x-asm'], ['aso', 'application/vnd.accpac.simply.aso'], ['asx', 'video/x-ms-asf'], ['atc', 'application/vnd.acucorp'], ['atom', 'application/atom+xml'], ['atomcat', 'application/atomcat+xml'], ['atomdeleted', 'application/atomdeleted+xml'], ['atomsvc', 'application/atomsvc+xml'], ['atx', 'application/vnd.antix.game-component'], ['au', 'audio/x-au'], ['avi', 'video/x-msvideo'], ['avif', 'image/avif'], ['aw', 'application/applixware'], ['azf', 'application/vnd.airzip.filesecure.azf'], ['azs', 'application/vnd.airzip.filesecure.azs'], ['azv', 'image/vnd.airzip.accelerator.azv'], ['azw', 'application/vnd.amazon.ebook'], ['b16', 'image/vnd.pco.b16'], ['bat', 'application/x-msdownload'], ['bcpio', 'application/x-bcpio'], ['bdf', 'application/x-font-bdf'], ['bdm', 'application/vnd.syncml.dm+wbxml'], ['bdoc', 'application/x-bdoc'], ['bed', 'application/vnd.realvnc.bed'], ['bh2', 'application/vnd.fujitsu.oasysprs'], ['bin', 'application/octet-stream'], ['blb', 'application/x-blorb'], ['blorb', 'application/x-blorb'], ['bmi', 'application/vnd.bmi'], ['bmml', 'application/vnd.balsamiq.bmml+xml'], ['bmp', 'image/bmp'], ['book', 'application/vnd.framemaker'], ['box', 'application/vnd.previewsystems.box'], ['boz', 'application/x-bzip2'], ['bpk', 'application/octet-stream'], ['bpmn', 'application/octet-stream'], ['bsp', 'model/vnd.valve.source.compiled-map'], ['btif', 'image/prs.btif'], ['buffer', 'application/octet-stream'], ['bz', 'application/x-bzip'], ['bz2', 'application/x-bzip2'], ['c', 'text/x-c'], ['c4d', 'application/vnd.clonk.c4group'], ['c4f', 'application/vnd.clonk.c4group'], ['c4g', 'application/vnd.clonk.c4group'], ['c4p', 'application/vnd.clonk.c4group'], ['c4u', 'application/vnd.clonk.c4group'], ['c11amc', 'application/vnd.cluetrust.cartomobile-config'], ['c11amz', 'application/vnd.cluetrust.cartomobile-config-pkg'], ['cab', 'application/vnd.ms-cab-compressed'], ['caf', 'audio/x-caf'], ['cap', 'application/vnd.tcpdump.pcap'], ['car', 'application/vnd.curl.car'], ['cat', 'application/vnd.ms-pki.seccat'], ['cb7', 'application/x-cbr'], ['cba', 'application/x-cbr'], ['cbr', 'application/x-cbr'], ['cbt', 'application/x-cbr'], ['cbz', 'application/x-cbr'], ['cc', 'text/x-c'], ['cco', 'application/x-cocoa'], ['cct', 'application/x-director'], ['ccxml', 'application/ccxml+xml'], ['cdbcmsg', 'application/vnd.contact.cmsg'], ['cda', 'application/x-cdf'], ['cdf', 'application/x-netcdf'], ['cdfx', 'application/cdfx+xml'], ['cdkey', 'application/vnd.mediastation.cdkey'], ['cdmia', 'application/cdmi-capability'], ['cdmic', 'application/cdmi-container'], ['cdmid', 'application/cdmi-domain'], ['cdmio', 'application/cdmi-object'], ['cdmiq', 'application/cdmi-queue'], ['cdr', 'application/cdr'], ['cdx', 'chemical/x-cdx'], ['cdxml', 'application/vnd.chemdraw+xml'], ['cdy', 'application/vnd.cinderella'], ['cer', 'application/pkix-cert'], ['cfs', 'application/x-cfs-compressed'], ['cgm', 'image/cgm'], ['chat', 'application/x-chat'], ['chm', 'application/vnd.ms-htmlhelp'], ['chrt', 'application/vnd.kde.kchart'], ['cif', 'chemical/x-cif'], ['cii', 'application/vnd.anser-web-certificate-issue-initiation'], ['cil', 'application/vnd.ms-artgalry'], ['cjs', 'application/node'], ['cla', 'application/vnd.claymore'], ['class', 'application/octet-stream'], ['clkk', 'application/vnd.crick.clicker.keyboard'], ['clkp', 'application/vnd.crick.clicker.palette'], ['clkt', 'application/vnd.crick.clicker.template'], ['clkw', 'application/vnd.crick.clicker.wordbank'], ['clkx', 'application/vnd.crick.clicker'], ['clp', 'application/x-msclip'], ['cmc', 'application/vnd.cosmocaller'], ['cmdf', 'chemical/x-cmdf'], ['cml', 'chemical/x-cml'], ['cmp', 'application/vnd.yellowriver-custom-menu'], ['cmx', 'image/x-cmx'], ['cod', 'application/vnd.rim.cod'], ['coffee', 'text/coffeescript'], ['com', 'application/x-msdownload'], ['conf', 'text/plain'], ['cpio', 'application/x-cpio'], ['cpp', 'text/x-c'], ['cpt', 'application/mac-compactpro'], ['crd', 'application/x-mscardfile'], ['crl', 'application/pkix-crl'], ['crt', 'application/x-x509-ca-cert'], ['crx', 'application/x-chrome-extension'], ['cryptonote', 'application/vnd.rig.cryptonote'], ['csh', 'application/x-csh'], ['csl', 'application/vnd.citationstyles.style+xml'], ['csml', 'chemical/x-csml'], ['csp', 'application/vnd.commonspace'], ['csr', 'application/octet-stream'], ['css', 'text/css'], ['cst', 'application/x-director'], ['csv', 'text/csv'], ['cu', 'application/cu-seeme'], ['curl', 'text/vnd.curl'], ['cww', 'application/prs.cww'], ['cxt', 'application/x-director'], ['cxx', 'text/x-c'], ['dae', 'model/vnd.collada+xml'], ['daf', 'application/vnd.mobius.daf'], ['dart', 'application/vnd.dart'], ['dataless', 'application/vnd.fdsn.seed'], ['davmount', 'application/davmount+xml'], ['dbf', 'application/vnd.dbf'], ['dbk', 'application/docbook+xml'], ['dcr', 'application/x-director'], ['dcurl', 'text/vnd.curl.dcurl'], ['dd2', 'application/vnd.oma.dd2+xml'], ['ddd', 'application/vnd.fujixerox.ddd'], ['ddf', 'application/vnd.syncml.dmddf+xml'], ['dds', 'image/vnd.ms-dds'], ['deb', 'application/x-debian-package'], ['def', 'text/plain'], ['deploy', 'application/octet-stream'], ['der', 'application/x-x509-ca-cert'], ['dfac', 'application/vnd.dreamfactory'], ['dgc', 'application/x-dgc-compressed'], ['dic', 'text/x-c'], ['dir', 'application/x-director'], ['dis', 'application/vnd.mobius.dis'], ['disposition-notification', 'message/disposition-notification'], ['dist', 'application/octet-stream'], ['distz', 'application/octet-stream'], ['djv', 'image/vnd.djvu'], ['djvu', 'image/vnd.djvu'], ['dll', 'application/octet-stream'], ['dmg', 'application/x-apple-diskimage'], ['dmn', 'application/octet-stream'], ['dmp', 'application/vnd.tcpdump.pcap'], ['dms', 'application/octet-stream'], ['dna', 'application/vnd.dna'], ['doc', 'application/msword'], ['docm', 'application/vnd.ms-word.template.macroEnabled.12'], ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'], ['dot', 'application/msword'], ['dotm', 'application/vnd.ms-word.template.macroEnabled.12'], ['dotx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.template'], ['dp', 'application/vnd.osgi.dp'], ['dpg', 'application/vnd.dpgraph'], ['dra', 'audio/vnd.dra'], ['drle', 'image/dicom-rle'], ['dsc', 'text/prs.lines.tag'], ['dssc', 'application/dssc+der'], ['dtb', 'application/x-dtbook+xml'], ['dtd', 'application/xml-dtd'], ['dts', 'audio/vnd.dts'], ['dtshd', 'audio/vnd.dts.hd'], ['dump', 'application/octet-stream'], ['dvb', 'video/vnd.dvb.file'], ['dvi', 'application/x-dvi'], ['dwd', 'application/atsc-dwd+xml'], ['dwf', 'model/vnd.dwf'], ['dwg', 'image/vnd.dwg'], ['dxf', 'image/vnd.dxf'], ['dxp', 'application/vnd.spotfire.dxp'], ['dxr', 'application/x-director'], ['ear', 'application/java-archive'], ['ecelp4800', 'audio/vnd.nuera.ecelp4800'], ['ecelp7470', 'audio/vnd.nuera.ecelp7470'], ['ecelp9600', 'audio/vnd.nuera.ecelp9600'], ['ecma', 'application/ecmascript'], ['edm', 'application/vnd.novadigm.edm'], ['edx', 'application/vnd.novadigm.edx'], ['efif', 'application/vnd.picsel'], ['ei6', 'application/vnd.pg.osasli'], ['elc', 'application/octet-stream'], ['emf', 'image/emf'], ['eml', 'message/rfc822'], ['emma', 'application/emma+xml'], ['emotionml', 'application/emotionml+xml'], ['emz', 'application/x-msmetafile'], ['eol', 'audio/vnd.digital-winds'], ['eot', 'application/vnd.ms-fontobject'], ['eps', 'application/postscript'], ['epub', 'application/epub+zip'], ['es', 'application/ecmascript'], ['es3', 'application/vnd.eszigno3+xml'], ['esa', 'application/vnd.osgi.subsystem'], ['esf', 'application/vnd.epson.esf'], ['et3', 'application/vnd.eszigno3+xml'], ['etx', 'text/x-setext'], ['eva', 'application/x-eva'], ['evy', 'application/x-envoy'], ['exe', 'application/octet-stream'], ['exi', 'application/exi'], ['exp', 'application/express'], ['exr', 'image/aces'], ['ext', 'application/vnd.novadigm.ext'], ['ez', 'application/andrew-inset'], ['ez2', 'application/vnd.ezpix-album'], ['ez3', 'application/vnd.ezpix-package'], ['f', 'text/x-fortran'], ['f4v', 'video/mp4'], ['f77', 'text/x-fortran'], ['f90', 'text/x-fortran'], ['fbs', 'image/vnd.fastbidsheet'], ['fcdt', 'application/vnd.adobe.formscentral.fcdt'], ['fcs', 'application/vnd.isac.fcs'], ['fdf', 'application/vnd.fdf'], ['fdt', 'application/fdt+xml'], ['fe_launch', 'application/vnd.denovo.fcselayout-link'], ['fg5', 'application/vnd.fujitsu.oasysgp'], ['fgd', 'application/x-director'], ['fh', 'image/x-freehand'], ['fh4', 'image/x-freehand'], ['fh5', 'image/x-freehand'], ['fh7', 'image/x-freehand'], ['fhc', 'image/x-freehand'], ['fig', 'application/x-xfig'], ['fits', 'image/fits'], ['flac', 'audio/x-flac'], ['fli', 'video/x-fli'], ['flo', 'application/vnd.micrografx.flo'], ['flv', 'video/x-flv'], ['flw', 'application/vnd.kde.kivio'], ['flx', 'text/vnd.fmi.flexstor'], ['fly', 'text/vnd.fly'], ['fm', 'application/vnd.framemaker'], ['fnc', 'application/vnd.frogans.fnc'], ['fo', 'application/vnd.software602.filler.form+xml'], ['for', 'text/x-fortran'], ['fpx', 'image/vnd.fpx'], ['frame', 'application/vnd.framemaker'], ['fsc', 'application/vnd.fsc.weblaunch'], ['fst', 'image/vnd.fst'], ['ftc', 'application/vnd.fluxtime.clip'], ['fti', 'application/vnd.anser-web-funds-transfer-initiation'], ['fvt', 'video/vnd.fvt'], ['fxp', 'application/vnd.adobe.fxp'], ['fxpl', 'application/vnd.adobe.fxp'], ['fzs', 'application/vnd.fuzzysheet'], ['g2w', 'application/vnd.geoplan'], ['g3', 'image/g3fax'], ['g3w', 'application/vnd.geospace'], ['gac', 'application/vnd.groove-account'], ['gam', 'application/x-tads'], ['gbr', 'application/rpki-ghostbusters'], ['gca', 'application/x-gca-compressed'], ['gdl', 'model/vnd.gdl'], ['gdoc', 'application/vnd.google-apps.document'], ['geo', 'application/vnd.dynageo'], ['geojson', 'application/geo+json'], ['gex', 'application/vnd.geometry-explorer'], ['ggb', 'application/vnd.geogebra.file'], ['ggt', 'application/vnd.geogebra.tool'], ['ghf', 'application/vnd.groove-help'], ['gif', 'image/gif'], ['gim', 'application/vnd.groove-identity-message'], ['glb', 'model/gltf-binary'], ['gltf', 'model/gltf+json'], ['gml', 'application/gml+xml'], ['gmx', 'application/vnd.gmx'], ['gnumeric', 'application/x-gnumeric'], ['gpg', 'application/gpg-keys'], ['gph', 'application/vnd.flographit'], ['gpx', 'application/gpx+xml'], ['gqf', 'application/vnd.grafeq'], ['gqs', 'application/vnd.grafeq'], ['gram', 'application/srgs'], ['gramps', 'application/x-gramps-xml'], ['gre', 'application/vnd.geometry-explorer'], ['grv', 'application/vnd.groove-injector'], ['grxml', 'application/srgs+xml'], ['gsf', 'application/x-font-ghostscript'], ['gsheet', 'application/vnd.google-apps.spreadsheet'], ['gslides', 'application/vnd.google-apps.presentation'], ['gtar', 'application/x-gtar'], ['gtm', 'application/vnd.groove-tool-message'], ['gtw', 'model/vnd.gtw'], ['gv', 'text/vnd.graphviz'], ['gxf', 'application/gxf'], ['gxt', 'application/vnd.geonext'], ['gz', 'application/gzip'], ['gzip', 'application/gzip'], ['h', 'text/x-c'], ['h261', 'video/h261'], ['h263', 'video/h263'], ['h264', 'video/h264'], ['hal', 'application/vnd.hal+xml'], ['hbci', 'application/vnd.hbci'], ['hbs', 'text/x-handlebars-template'], ['hdd', 'application/x-virtualbox-hdd'], ['hdf', 'application/x-hdf'], ['heic', 'image/heic'], ['heics', 'image/heic-sequence'], ['heif', 'image/heif'], ['heifs', 'image/heif-sequence'], ['hej2', 'image/hej2k'], ['held', 'application/atsc-held+xml'], ['hh', 'text/x-c'], ['hjson', 'application/hjson'], ['hlp', 'application/winhlp'], ['hpgl', 'application/vnd.hp-hpgl'], ['hpid', 'application/vnd.hp-hpid'], ['hps', 'application/vnd.hp-hps'], ['hqx', 'application/mac-binhex40'], ['hsj2', 'image/hsj2'], ['htc', 'text/x-component'], ['htke', 'application/vnd.kenameaapp'], ['htm', 'text/html'], ['html', 'text/html'], ['hvd', 'application/vnd.yamaha.hv-dic'], ['hvp', 'application/vnd.yamaha.hv-voice'], ['hvs', 'application/vnd.yamaha.hv-script'], ['i2g', 'application/vnd.intergeo'], ['icc', 'application/vnd.iccprofile'], ['ice', 'x-conference/x-cooltalk'], ['icm', 'application/vnd.iccprofile'], ['ico', 'image/x-icon'], ['ics', 'text/calendar'], ['ief', 'image/ief'], ['ifb', 'text/calendar'], ['ifm', 'application/vnd.shana.informed.formdata'], ['iges', 'model/iges'], ['igl', 'application/vnd.igloader'], ['igm', 'application/vnd.insors.igm'], ['igs', 'model/iges'], ['igx', 'application/vnd.micrografx.igx'], ['iif', 'application/vnd.shana.informed.interchange'], ['img', 'application/octet-stream'], ['imp', 'application/vnd.accpac.simply.imp'], ['ims', 'application/vnd.ms-ims'], ['in', 'text/plain'], ['ini', 'text/plain'], ['ink', 'application/inkml+xml'], ['inkml', 'application/inkml+xml'], ['install', 'application/x-install-instructions'], ['iota', 'application/vnd.astraea-software.iota'], ['ipfix', 'application/ipfix'], ['ipk', 'application/vnd.shana.informed.package'], ['irm', 'application/vnd.ibm.rights-management'], ['irp', 'application/vnd.irepository.package+xml'], ['iso', 'application/x-iso9660-image'], ['itp', 'application/vnd.shana.informed.formtemplate'], ['its', 'application/its+xml'], ['ivp', 'application/vnd.immervision-ivp'], ['ivu', 'application/vnd.immervision-ivu'], ['jad', 'text/vnd.sun.j2me.app-descriptor'], ['jade', 'text/jade'], ['jam', 'application/vnd.jam'], ['jar', 'application/java-archive'], ['jardiff', 'application/x-java-archive-diff'], ['java', 'text/x-java-source'], ['jhc', 'image/jphc'], ['jisp', 'application/vnd.jisp'], ['jls', 'image/jls'], ['jlt', 'application/vnd.hp-jlyt'], ['jng', 'image/x-jng'], ['jnlp', 'application/x-java-jnlp-file'], ['joda', 'application/vnd.joost.joda-archive'], ['jp2', 'image/jp2'], ['jpe', 'image/jpeg'], ['jpeg', 'image/jpeg'], ['jpf', 'image/jpx'], ['jpg', 'image/jpeg'], ['jpg2', 'image/jp2'], ['jpgm', 'video/jpm'], ['jpgv', 'video/jpeg'], ['jph', 'image/jph'], ['jpm', 'video/jpm'], ['jpx', 'image/jpx'], ['js', 'application/javascript'], ['json', 'application/json'], ['json5', 'application/json5'], ['jsonld', 'application/ld+json'],\n// https://jsonlines.org/\n['jsonl', 'application/jsonl'], ['jsonml', 'application/jsonml+json'], ['jsx', 'text/jsx'], ['jxr', 'image/jxr'], ['jxra', 'image/jxra'], ['jxrs', 'image/jxrs'], ['jxs', 'image/jxs'], ['jxsc', 'image/jxsc'], ['jxsi', 'image/jxsi'], ['jxss', 'image/jxss'], ['kar', 'audio/midi'], ['karbon', 'application/vnd.kde.karbon'], ['kdb', 'application/octet-stream'], ['kdbx', 'application/x-keepass2'], ['key', 'application/x-iwork-keynote-sffkey'], ['kfo', 'application/vnd.kde.kformula'], ['kia', 'application/vnd.kidspiration'], ['kml', 'application/vnd.google-earth.kml+xml'], ['kmz', 'application/vnd.google-earth.kmz'], ['kne', 'application/vnd.kinar'], ['knp', 'application/vnd.kinar'], ['kon', 'application/vnd.kde.kontour'], ['kpr', 'application/vnd.kde.kpresenter'], ['kpt', 'application/vnd.kde.kpresenter'], ['kpxx', 'application/vnd.ds-keypoint'], ['ksp', 'application/vnd.kde.kspread'], ['ktr', 'application/vnd.kahootz'], ['ktx', 'image/ktx'], ['ktx2', 'image/ktx2'], ['ktz', 'application/vnd.kahootz'], ['kwd', 'application/vnd.kde.kword'], ['kwt', 'application/vnd.kde.kword'], ['lasxml', 'application/vnd.las.las+xml'], ['latex', 'application/x-latex'], ['lbd', 'application/vnd.llamagraphics.life-balance.desktop'], ['lbe', 'application/vnd.llamagraphics.life-balance.exchange+xml'], ['les', 'application/vnd.hhe.lesson-player'], ['less', 'text/less'], ['lgr', 'application/lgr+xml'], ['lha', 'application/octet-stream'], ['link66', 'application/vnd.route66.link66+xml'], ['list', 'text/plain'], ['list3820', 'application/vnd.ibm.modcap'], ['listafp', 'application/vnd.ibm.modcap'], ['litcoffee', 'text/coffeescript'], ['lnk', 'application/x-ms-shortcut'], ['log', 'text/plain'], ['lostxml', 'application/lost+xml'], ['lrf', 'application/octet-stream'], ['lrm', 'application/vnd.ms-lrm'], ['ltf', 'application/vnd.frogans.ltf'], ['lua', 'text/x-lua'], ['luac', 'application/x-lua-bytecode'], ['lvp', 'audio/vnd.lucent.voice'], ['lwp', 'application/vnd.lotus-wordpro'], ['lzh', 'application/octet-stream'], ['m1v', 'video/mpeg'], ['m2a', 'audio/mpeg'], ['m2v', 'video/mpeg'], ['m3a', 'audio/mpeg'], ['m3u', 'text/plain'], ['m3u8', 'application/vnd.apple.mpegurl'], ['m4a', 'audio/x-m4a'], ['m4p', 'application/mp4'], ['m4s', 'video/iso.segment'], ['m4u', 'application/vnd.mpegurl'], ['m4v', 'video/x-m4v'], ['m13', 'application/x-msmediaview'], ['m14', 'application/x-msmediaview'], ['m21', 'application/mp21'], ['ma', 'application/mathematica'], ['mads', 'application/mads+xml'], ['maei', 'application/mmt-aei+xml'], ['mag', 'application/vnd.ecowin.chart'], ['maker', 'application/vnd.framemaker'], ['man', 'text/troff'], ['manifest', 'text/cache-manifest'], ['map', 'application/json'], ['mar', 'application/octet-stream'], ['markdown', 'text/markdown'], ['mathml', 'application/mathml+xml'], ['mb', 'application/mathematica'], ['mbk', 'application/vnd.mobius.mbk'], ['mbox', 'application/mbox'], ['mc1', 'application/vnd.medcalcdata'], ['mcd', 'application/vnd.mcd'], ['mcurl', 'text/vnd.curl.mcurl'], ['md', 'text/markdown'], ['mdb', 'application/x-msaccess'], ['mdi', 'image/vnd.ms-modi'], ['mdx', 'text/mdx'], ['me', 'text/troff'], ['mesh', 'model/mesh'], ['meta4', 'application/metalink4+xml'], ['metalink', 'application/metalink+xml'], ['mets', 'application/mets+xml'], ['mfm', 'application/vnd.mfmp'], ['mft', 'application/rpki-manifest'], ['mgp', 'application/vnd.osgeo.mapguide.package'], ['mgz', 'application/vnd.proteus.magazine'], ['mid', 'audio/midi'], ['midi', 'audio/midi'], ['mie', 'application/x-mie'], ['mif', 'application/vnd.mif'], ['mime', 'message/rfc822'], ['mj2', 'video/mj2'], ['mjp2', 'video/mj2'], ['mjs', 'application/javascript'], ['mk3d', 'video/x-matroska'], ['mka', 'audio/x-matroska'], ['mkd', 'text/x-markdown'], ['mks', 'video/x-matroska'], ['mkv', 'video/x-matroska'], ['mlp', 'application/vnd.dolby.mlp'], ['mmd', 'application/vnd.chipnuts.karaoke-mmd'], ['mmf', 'application/vnd.smaf'], ['mml', 'text/mathml'], ['mmr', 'image/vnd.fujixerox.edmics-mmr'], ['mng', 'video/x-mng'], ['mny', 'application/x-msmoney'], ['mobi', 'application/x-mobipocket-ebook'], ['mods', 'application/mods+xml'], ['mov', 'video/quicktime'], ['movie', 'video/x-sgi-movie'], ['mp2', 'audio/mpeg'], ['mp2a', 'audio/mpeg'], ['mp3', 'audio/mpeg'], ['mp4', 'video/mp4'], ['mp4a', 'audio/mp4'], ['mp4s', 'application/mp4'], ['mp4v', 'video/mp4'], ['mp21', 'application/mp21'], ['mpc', 'application/vnd.mophun.certificate'], ['mpd', 'application/dash+xml'], ['mpe', 'video/mpeg'], ['mpeg', 'video/mpeg'], ['mpg', 'video/mpeg'], ['mpg4', 'video/mp4'], ['mpga', 'audio/mpeg'], ['mpkg', 'application/vnd.apple.installer+xml'], ['mpm', 'application/vnd.blueice.multipass'], ['mpn', 'application/vnd.mophun.application'], ['mpp', 'application/vnd.ms-project'], ['mpt', 'application/vnd.ms-project'], ['mpy', 'application/vnd.ibm.minipay'], ['mqy', 'application/vnd.mobius.mqy'], ['mrc', 'application/marc'], ['mrcx', 'application/marcxml+xml'], ['ms', 'text/troff'], ['mscml', 'application/mediaservercontrol+xml'], ['mseed', 'application/vnd.fdsn.mseed'], ['mseq', 'application/vnd.mseq'], ['msf', 'application/vnd.epson.msf'], ['msg', 'application/vnd.ms-outlook'], ['msh', 'model/mesh'], ['msi', 'application/x-msdownload'], ['msl', 'application/vnd.mobius.msl'], ['msm', 'application/octet-stream'], ['msp', 'application/octet-stream'], ['msty', 'application/vnd.muvee.style'], ['mtl', 'model/mtl'], ['mts', 'model/vnd.mts'], ['mus', 'application/vnd.musician'], ['musd', 'application/mmt-usd+xml'], ['musicxml', 'application/vnd.recordare.musicxml+xml'], ['mvb', 'application/x-msmediaview'], ['mvt', 'application/vnd.mapbox-vector-tile'], ['mwf', 'application/vnd.mfer'], ['mxf', 'application/mxf'], ['mxl', 'application/vnd.recordare.musicxml'], ['mxmf', 'audio/mobile-xmf'], ['mxml', 'application/xv+xml'], ['mxs', 'application/vnd.triscape.mxs'], ['mxu', 'video/vnd.mpegurl'], ['n-gage', 'application/vnd.nokia.n-gage.symbian.install'], ['n3', 'text/n3'], ['nb', 'application/mathematica'], ['nbp', 'application/vnd.wolfram.player'], ['nc', 'application/x-netcdf'], ['ncx', 'application/x-dtbncx+xml'], ['nfo', 'text/x-nfo'], ['ngdat', 'application/vnd.nokia.n-gage.data'], ['nitf', 'application/vnd.nitf'], ['nlu', 'application/vnd.neurolanguage.nlu'], ['nml', 'application/vnd.enliven'], ['nnd', 'application/vnd.noblenet-directory'], ['nns', 'application/vnd.noblenet-sealer'], ['nnw', 'application/vnd.noblenet-web'], ['npx', 'image/vnd.net-fpx'], ['nq', 'application/n-quads'], ['nsc', 'application/x-conference'], ['nsf', 'application/vnd.lotus-notes'], ['nt', 'application/n-triples'], ['ntf', 'application/vnd.nitf'], ['numbers', 'application/x-iwork-numbers-sffnumbers'], ['nzb', 'application/x-nzb'], ['oa2', 'application/vnd.fujitsu.oasys2'], ['oa3', 'application/vnd.fujitsu.oasys3'], ['oas', 'application/vnd.fujitsu.oasys'], ['obd', 'application/x-msbinder'], ['obgx', 'application/vnd.openblox.game+xml'], ['obj', 'model/obj'], ['oda', 'application/oda'], ['odb', 'application/vnd.oasis.opendocument.database'], ['odc', 'application/vnd.oasis.opendocument.chart'], ['odf', 'application/vnd.oasis.opendocument.formula'], ['odft', 'application/vnd.oasis.opendocument.formula-template'], ['odg', 'application/vnd.oasis.opendocument.graphics'], ['odi', 'application/vnd.oasis.opendocument.image'], ['odm', 'application/vnd.oasis.opendocument.text-master'], ['odp', 'application/vnd.oasis.opendocument.presentation'], ['ods', 'application/vnd.oasis.opendocument.spreadsheet'], ['odt', 'application/vnd.oasis.opendocument.text'], ['oga', 'audio/ogg'], ['ogex', 'model/vnd.opengex'], ['ogg', 'audio/ogg'], ['ogv', 'video/ogg'], ['ogx', 'application/ogg'], ['omdoc', 'application/omdoc+xml'], ['onepkg', 'application/onenote'], ['onetmp', 'application/onenote'], ['onetoc', 'application/onenote'], ['onetoc2', 'application/onenote'], ['opf', 'application/oebps-package+xml'], ['opml', 'text/x-opml'], ['oprc', 'application/vnd.palm'], ['opus', 'audio/ogg'], ['org', 'text/x-org'], ['osf', 'application/vnd.yamaha.openscoreformat'], ['osfpvg', 'application/vnd.yamaha.openscoreformat.osfpvg+xml'], ['osm', 'application/vnd.openstreetmap.data+xml'], ['otc', 'application/vnd.oasis.opendocument.chart-template'], ['otf', 'font/otf'], ['otg', 'application/vnd.oasis.opendocument.graphics-template'], ['oth', 'application/vnd.oasis.opendocument.text-web'], ['oti', 'application/vnd.oasis.opendocument.image-template'], ['otp', 'application/vnd.oasis.opendocument.presentation-template'], ['ots', 'application/vnd.oasis.opendocument.spreadsheet-template'], ['ott', 'application/vnd.oasis.opendocument.text-template'], ['ova', 'application/x-virtualbox-ova'], ['ovf', 'application/x-virtualbox-ovf'], ['owl', 'application/rdf+xml'], ['oxps', 'application/oxps'], ['oxt', 'application/vnd.openofficeorg.extension'], ['p', 'text/x-pascal'], ['p7a', 'application/x-pkcs7-signature'], ['p7b', 'application/x-pkcs7-certificates'], ['p7c', 'application/pkcs7-mime'], ['p7m', 'application/pkcs7-mime'], ['p7r', 'application/x-pkcs7-certreqresp'], ['p7s', 'application/pkcs7-signature'], ['p8', 'application/pkcs8'], ['p10', 'application/x-pkcs10'], ['p12', 'application/x-pkcs12'], ['pac', 'application/x-ns-proxy-autoconfig'], ['pages', 'application/x-iwork-pages-sffpages'], ['pas', 'text/x-pascal'], ['paw', 'application/vnd.pawaafile'], ['pbd', 'application/vnd.powerbuilder6'], ['pbm', 'image/x-portable-bitmap'], ['pcap', 'application/vnd.tcpdump.pcap'], ['pcf', 'application/x-font-pcf'], ['pcl', 'application/vnd.hp-pcl'], ['pclxl', 'application/vnd.hp-pclxl'], ['pct', 'image/x-pict'], ['pcurl', 'application/vnd.curl.pcurl'], ['pcx', 'image/x-pcx'], ['pdb', 'application/x-pilot'], ['pde', 'text/x-processing'], ['pdf', 'application/pdf'], ['pem', 'application/x-x509-user-cert'], ['pfa', 'application/x-font-type1'], ['pfb', 'application/x-font-type1'], ['pfm', 'application/x-font-type1'], ['pfr', 'application/font-tdpfr'], ['pfx', 'application/x-pkcs12'], ['pgm', 'image/x-portable-graymap'], ['pgn', 'application/x-chess-pgn'], ['pgp', 'application/pgp'], ['php', 'application/x-httpd-php'], ['php3', 'application/x-httpd-php'], ['php4', 'application/x-httpd-php'], ['phps', 'application/x-httpd-php-source'], ['phtml', 'application/x-httpd-php'], ['pic', 'image/x-pict'], ['pkg', 'application/octet-stream'], ['pki', 'application/pkixcmp'], ['pkipath', 'application/pkix-pkipath'], ['pkpass', 'application/vnd.apple.pkpass'], ['pl', 'application/x-perl'], ['plb', 'application/vnd.3gpp.pic-bw-large'], ['plc', 'application/vnd.mobius.plc'], ['plf', 'application/vnd.pocketlearn'], ['pls', 'application/pls+xml'], ['pm', 'application/x-perl'], ['pml', 'application/vnd.ctc-posml'], ['png', 'image/png'], ['pnm', 'image/x-portable-anymap'], ['portpkg', 'application/vnd.macports.portpkg'], ['pot', 'application/vnd.ms-powerpoint'], ['potm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'], ['potx', 'application/vnd.openxmlformats-officedocument.presentationml.template'], ['ppa', 'application/vnd.ms-powerpoint'], ['ppam', 'application/vnd.ms-powerpoint.addin.macroEnabled.12'], ['ppd', 'application/vnd.cups-ppd'], ['ppm', 'image/x-portable-pixmap'], ['pps', 'application/vnd.ms-powerpoint'], ['ppsm', 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12'], ['ppsx', 'application/vnd.openxmlformats-officedocument.presentationml.slideshow'], ['ppt', 'application/powerpoint'], ['pptm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'], ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'], ['pqa', 'application/vnd.palm'], ['prc', 'application/x-pilot'], ['pre', 'application/vnd.lotus-freelance'], ['prf', 'application/pics-rules'], ['provx', 'application/provenance+xml'], ['ps', 'application/postscript'], ['psb', 'application/vnd.3gpp.pic-bw-small'], ['psd', 'application/x-photoshop'], ['psf', 'application/x-font-linux-psf'], ['pskcxml', 'application/pskc+xml'], ['pti', 'image/prs.pti'], ['ptid', 'application/vnd.pvi.ptid1'], ['pub', 'application/x-mspublisher'], ['pvb', 'application/vnd.3gpp.pic-bw-var'], ['pwn', 'application/vnd.3m.post-it-notes'], ['pya', 'audio/vnd.ms-playready.media.pya'], ['pyv', 'video/vnd.ms-playready.media.pyv'], ['qam', 'application/vnd.epson.quickanime'], ['qbo', 'application/vnd.intu.qbo'], ['qfx', 'application/vnd.intu.qfx'], ['qps', 'application/vnd.publishare-delta-tree'], ['qt', 'video/quicktime'], ['qwd', 'application/vnd.quark.quarkxpress'], ['qwt', 'application/vnd.quark.quarkxpress'], ['qxb', 'application/vnd.quark.quarkxpress'], ['qxd', 'application/vnd.quark.quarkxpress'], ['qxl', 'application/vnd.quark.quarkxpress'], ['qxt', 'application/vnd.quark.quarkxpress'], ['ra', 'audio/x-realaudio'], ['ram', 'audio/x-pn-realaudio'], ['raml', 'application/raml+yaml'], ['rapd', 'application/route-apd+xml'], ['rar', 'application/x-rar'], ['ras', 'image/x-cmu-raster'], ['rcprofile', 'application/vnd.ipunplugged.rcprofile'], ['rdf', 'application/rdf+xml'], ['rdz', 'application/vnd.data-vision.rdz'], ['relo', 'application/p2p-overlay+xml'], ['rep', 'application/vnd.businessobjects'], ['res', 'application/x-dtbresource+xml'], ['rgb', 'image/x-rgb'], ['rif', 'application/reginfo+xml'], ['rip', 'audio/vnd.rip'], ['ris', 'application/x-research-info-systems'], ['rl', 'application/resource-lists+xml'], ['rlc', 'image/vnd.fujixerox.edmics-rlc'], ['rld', 'application/resource-lists-diff+xml'], ['rm', 'audio/x-pn-realaudio'], ['rmi', 'audio/midi'], ['rmp', 'audio/x-pn-realaudio-plugin'], ['rms', 'application/vnd.jcp.javame.midlet-rms'], ['rmvb', 'application/vnd.rn-realmedia-vbr'], ['rnc', 'application/relax-ng-compact-syntax'], ['rng', 'application/xml'], ['roa', 'application/rpki-roa'], ['roff', 'text/troff'], ['rp9', 'application/vnd.cloanto.rp9'], ['rpm', 'audio/x-pn-realaudio-plugin'], ['rpss', 'application/vnd.nokia.radio-presets'], ['rpst', 'application/vnd.nokia.radio-preset'], ['rq', 'application/sparql-query'], ['rs', 'application/rls-services+xml'], ['rsa', 'application/x-pkcs7'], ['rsat', 'application/atsc-rsat+xml'], ['rsd', 'application/rsd+xml'], ['rsheet', 'application/urc-ressheet+xml'], ['rss', 'application/rss+xml'], ['rtf', 'text/rtf'], ['rtx', 'text/richtext'], ['run', 'application/x-makeself'], ['rusd', 'application/route-usd+xml'], ['rv', 'video/vnd.rn-realvideo'], ['s', 'text/x-asm'], ['s3m', 'audio/s3m'], ['saf', 'application/vnd.yamaha.smaf-audio'], ['sass', 'text/x-sass'], ['sbml', 'application/sbml+xml'], ['sc', 'application/vnd.ibm.secure-container'], ['scd', 'application/x-msschedule'], ['scm', 'application/vnd.lotus-screencam'], ['scq', 'application/scvp-cv-request'], ['scs', 'application/scvp-cv-response'], ['scss', 'text/x-scss'], ['scurl', 'text/vnd.curl.scurl'], ['sda', 'application/vnd.stardivision.draw'], ['sdc', 'application/vnd.stardivision.calc'], ['sdd', 'application/vnd.stardivision.impress'], ['sdkd', 'application/vnd.solent.sdkm+xml'], ['sdkm', 'application/vnd.solent.sdkm+xml'], ['sdp', 'application/sdp'], ['sdw', 'application/vnd.stardivision.writer'], ['sea', 'application/octet-stream'], ['see', 'application/vnd.seemail'], ['seed', 'application/vnd.fdsn.seed'], ['sema', 'application/vnd.sema'], ['semd', 'application/vnd.semd'], ['semf', 'application/vnd.semf'], ['senmlx', 'application/senml+xml'], ['sensmlx', 'application/sensml+xml'], ['ser', 'application/java-serialized-object'], ['setpay', 'application/set-payment-initiation'], ['setreg', 'application/set-registration-initiation'], ['sfd-hdstx', 'application/vnd.hydrostatix.sof-data'], ['sfs', 'application/vnd.spotfire.sfs'], ['sfv', 'text/x-sfv'], ['sgi', 'image/sgi'], ['sgl', 'application/vnd.stardivision.writer-global'], ['sgm', 'text/sgml'], ['sgml', 'text/sgml'], ['sh', 'application/x-sh'], ['shar', 'application/x-shar'], ['shex', 'text/shex'], ['shf', 'application/shf+xml'], ['shtml', 'text/html'], ['sid', 'image/x-mrsid-image'], ['sieve', 'application/sieve'], ['sig', 'application/pgp-signature'], ['sil', 'audio/silk'], ['silo', 'model/mesh'], ['sis', 'application/vnd.symbian.install'], ['sisx', 'application/vnd.symbian.install'], ['sit', 'application/x-stuffit'], ['sitx', 'application/x-stuffitx'], ['siv', 'application/sieve'], ['skd', 'application/vnd.koan'], ['skm', 'application/vnd.koan'], ['skp', 'application/vnd.koan'], ['skt', 'application/vnd.koan'], ['sldm', 'application/vnd.ms-powerpoint.slide.macroenabled.12'], ['sldx', 'application/vnd.openxmlformats-officedocument.presentationml.slide'], ['slim', 'text/slim'], ['slm', 'text/slim'], ['sls', 'application/route-s-tsid+xml'], ['slt', 'application/vnd.epson.salt'], ['sm', 'application/vnd.stepmania.stepchart'], ['smf', 'application/vnd.stardivision.math'], ['smi', 'application/smil'], ['smil', 'application/smil'], ['smv', 'video/x-smv'], ['smzip', 'application/vnd.stepmania.package'], ['snd', 'audio/basic'], ['snf', 'application/x-font-snf'], ['so', 'application/octet-stream'], ['spc', 'application/x-pkcs7-certificates'], ['spdx', 'text/spdx'], ['spf', 'application/vnd.yamaha.smaf-phrase'], ['spl', 'application/x-futuresplash'], ['spot', 'text/vnd.in3d.spot'], ['spp', 'application/scvp-vp-response'], ['spq', 'application/scvp-vp-request'], ['spx', 'audio/ogg'], ['sql', 'application/x-sql'], ['src', 'application/x-wais-source'], ['srt', 'application/x-subrip'], ['sru', 'application/sru+xml'], ['srx', 'application/sparql-results+xml'], ['ssdl', 'application/ssdl+xml'], ['sse', 'application/vnd.kodak-descriptor'], ['ssf', 'application/vnd.epson.ssf'], ['ssml', 'application/ssml+xml'], ['sst', 'application/octet-stream'], ['st', 'application/vnd.sailingtracker.track'], ['stc', 'application/vnd.sun.xml.calc.template'], ['std', 'application/vnd.sun.xml.draw.template'], ['stf', 'application/vnd.wt.stf'], ['sti', 'application/vnd.sun.xml.impress.template'], ['stk', 'application/hyperstudio'], ['stl', 'model/stl'], ['stpx', 'model/step+xml'], ['stpxz', 'model/step-xml+zip'], ['stpz', 'model/step+zip'], ['str', 'application/vnd.pg.format'], ['stw', 'application/vnd.sun.xml.writer.template'], ['styl', 'text/stylus'], ['stylus', 'text/stylus'], ['sub', 'text/vnd.dvb.subtitle'], ['sus', 'application/vnd.sus-calendar'], ['susp', 'application/vnd.sus-calendar'], ['sv4cpio', 'application/x-sv4cpio'], ['sv4crc', 'application/x-sv4crc'], ['svc', 'application/vnd.dvb.service'], ['svd', 'application/vnd.svd'], ['svg', 'image/svg+xml'], ['svgz', 'image/svg+xml'], ['swa', 'application/x-director'], ['swf', 'application/x-shockwave-flash'], ['swi', 'application/vnd.aristanetworks.swi'], ['swidtag', 'application/swid+xml'], ['sxc', 'application/vnd.sun.xml.calc'], ['sxd', 'application/vnd.sun.xml.draw'], ['sxg', 'application/vnd.sun.xml.writer.global'], ['sxi', 'application/vnd.sun.xml.impress'], ['sxm', 'application/vnd.sun.xml.math'], ['sxw', 'application/vnd.sun.xml.writer'], ['t', 'text/troff'], ['t3', 'application/x-t3vm-image'], ['t38', 'image/t38'], ['taglet', 'application/vnd.mynfc'], ['tao', 'application/vnd.tao.intent-module-archive'], ['tap', 'image/vnd.tencent.tap'], ['tar', 'application/x-tar'], ['tcap', 'application/vnd.3gpp2.tcap'], ['tcl', 'application/x-tcl'], ['td', 'application/urc-targetdesc+xml'], ['teacher', 'application/vnd.smart.teacher'], ['tei', 'application/tei+xml'], ['teicorpus', 'application/tei+xml'], ['tex', 'application/x-tex'], ['texi', 'application/x-texinfo'], ['texinfo', 'application/x-texinfo'], ['text', 'text/plain'], ['tfi', 'application/thraud+xml'], ['tfm', 'application/x-tex-tfm'], ['tfx', 'image/tiff-fx'], ['tga', 'image/x-tga'], ['tgz', 'application/x-tar'], ['thmx', 'application/vnd.ms-officetheme'], ['tif', 'image/tiff'], ['tiff', 'image/tiff'], ['tk', 'application/x-tcl'], ['tmo', 'application/vnd.tmobile-livetv'], ['toml', 'application/toml'], ['torrent', 'application/x-bittorrent'], ['tpl', 'application/vnd.groove-tool-template'], ['tpt', 'application/vnd.trid.tpt'], ['tr', 'text/troff'], ['tra', 'application/vnd.trueapp'], ['trig', 'application/trig'], ['trm', 'application/x-msterminal'], ['ts', 'video/mp2t'], ['tsd', 'application/timestamped-data'], ['tsv', 'text/tab-separated-values'], ['ttc', 'font/collection'], ['ttf', 'font/ttf'], ['ttl', 'text/turtle'], ['ttml', 'application/ttml+xml'], ['twd', 'application/vnd.simtech-mindmapper'], ['twds', 'application/vnd.simtech-mindmapper'], ['txd', 'application/vnd.genomatix.tuxedo'], ['txf', 'application/vnd.mobius.txf'], ['txt', 'text/plain'], ['u8dsn', 'message/global-delivery-status'], ['u8hdr', 'message/global-headers'], ['u8mdn', 'message/global-disposition-notification'], ['u8msg', 'message/global'], ['u32', 'application/x-authorware-bin'], ['ubj', 'application/ubjson'], ['udeb', 'application/x-debian-package'], ['ufd', 'application/vnd.ufdl'], ['ufdl', 'application/vnd.ufdl'], ['ulx', 'application/x-glulx'], ['umj', 'application/vnd.umajin'], ['unityweb', 'application/vnd.unity'], ['uoml', 'application/vnd.uoml+xml'], ['uri', 'text/uri-list'], ['uris', 'text/uri-list'], ['urls', 'text/uri-list'], ['usdz', 'model/vnd.usdz+zip'], ['ustar', 'application/x-ustar'], ['utz', 'application/vnd.uiq.theme'], ['uu', 'text/x-uuencode'], ['uva', 'audio/vnd.dece.audio'], ['uvd', 'application/vnd.dece.data'], ['uvf', 'application/vnd.dece.data'], ['uvg', 'image/vnd.dece.graphic'], ['uvh', 'video/vnd.dece.hd'], ['uvi', 'image/vnd.dece.graphic'], ['uvm', 'video/vnd.dece.mobile'], ['uvp', 'video/vnd.dece.pd'], ['uvs', 'video/vnd.dece.sd'], ['uvt', 'application/vnd.dece.ttml+xml'], ['uvu', 'video/vnd.uvvu.mp4'], ['uvv', 'video/vnd.dece.video'], ['uvva', 'audio/vnd.dece.audio'], ['uvvd', 'application/vnd.dece.data'], ['uvvf', 'application/vnd.dece.data'], ['uvvg', 'image/vnd.dece.graphic'], ['uvvh', 'video/vnd.dece.hd'], ['uvvi', 'image/vnd.dece.graphic'], ['uvvm', 'video/vnd.dece.mobile'], ['uvvp', 'video/vnd.dece.pd'], ['uvvs', 'video/vnd.dece.sd'], ['uvvt', 'application/vnd.dece.ttml+xml'], ['uvvu', 'video/vnd.uvvu.mp4'], ['uvvv', 'video/vnd.dece.video'], ['uvvx', 'application/vnd.dece.unspecified'], ['uvvz', 'application/vnd.dece.zip'], ['uvx', 'application/vnd.dece.unspecified'], ['uvz', 'application/vnd.dece.zip'], ['vbox', 'application/x-virtualbox-vbox'], ['vbox-extpack', 'application/x-virtualbox-vbox-extpack'], ['vcard', 'text/vcard'], ['vcd', 'application/x-cdlink'], ['vcf', 'text/x-vcard'], ['vcg', 'application/vnd.groove-vcard'], ['vcs', 'text/x-vcalendar'], ['vcx', 'application/vnd.vcx'], ['vdi', 'application/x-virtualbox-vdi'], ['vds', 'model/vnd.sap.vds'], ['vhd', 'application/x-virtualbox-vhd'], ['vis', 'application/vnd.visionary'], ['viv', 'video/vnd.vivo'], ['vlc', 'application/videolan'], ['vmdk', 'application/x-virtualbox-vmdk'], ['vob', 'video/x-ms-vob'], ['vor', 'application/vnd.stardivision.writer'], ['vox', 'application/x-authorware-bin'], ['vrml', 'model/vrml'], ['vsd', 'application/vnd.visio'], ['vsf', 'application/vnd.vsf'], ['vss', 'application/vnd.visio'], ['vst', 'application/vnd.visio'], ['vsw', 'application/vnd.visio'], ['vtf', 'image/vnd.valve.source.texture'], ['vtt', 'text/vtt'], ['vtu', 'model/vnd.vtu'], ['vxml', 'application/voicexml+xml'], ['w3d', 'application/x-director'], ['wad', 'application/x-doom'], ['wadl', 'application/vnd.sun.wadl+xml'], ['war', 'application/java-archive'], ['wasm', 'application/wasm'], ['wav', 'audio/x-wav'], ['wax', 'audio/x-ms-wax'], ['wbmp', 'image/vnd.wap.wbmp'], ['wbs', 'application/vnd.criticaltools.wbs+xml'], ['wbxml', 'application/wbxml'], ['wcm', 'application/vnd.ms-works'], ['wdb', 'application/vnd.ms-works'], ['wdp', 'image/vnd.ms-photo'], ['weba', 'audio/webm'], ['webapp', 'application/x-web-app-manifest+json'], ['webm', 'video/webm'], ['webmanifest', 'application/manifest+json'], ['webp', 'image/webp'], ['wg', 'application/vnd.pmi.widget'], ['wgt', 'application/widget'], ['wks', 'application/vnd.ms-works'], ['wm', 'video/x-ms-wm'], ['wma', 'audio/x-ms-wma'], ['wmd', 'application/x-ms-wmd'], ['wmf', 'image/wmf'], ['wml', 'text/vnd.wap.wml'], ['wmlc', 'application/wmlc'], ['wmls', 'text/vnd.wap.wmlscript'], ['wmlsc', 'application/vnd.wap.wmlscriptc'], ['wmv', 'video/x-ms-wmv'], ['wmx', 'video/x-ms-wmx'], ['wmz', 'application/x-msmetafile'], ['woff', 'font/woff'], ['woff2', 'font/woff2'], ['word', 'application/msword'], ['wpd', 'application/vnd.wordperfect'], ['wpl', 'application/vnd.ms-wpl'], ['wps', 'application/vnd.ms-works'], ['wqd', 'application/vnd.wqd'], ['wri', 'application/x-mswrite'], ['wrl', 'model/vrml'], ['wsc', 'message/vnd.wfa.wsc'], ['wsdl', 'application/wsdl+xml'], ['wspolicy', 'application/wspolicy+xml'], ['wtb', 'application/vnd.webturbo'], ['wvx', 'video/x-ms-wvx'], ['x3d', 'model/x3d+xml'], ['x3db', 'model/x3d+fastinfoset'], ['x3dbz', 'model/x3d+binary'], ['x3dv', 'model/x3d-vrml'], ['x3dvz', 'model/x3d+vrml'], ['x3dz', 'model/x3d+xml'], ['x32', 'application/x-authorware-bin'], ['x_b', 'model/vnd.parasolid.transmit.binary'], ['x_t', 'model/vnd.parasolid.transmit.text'], ['xaml', 'application/xaml+xml'], ['xap', 'application/x-silverlight-app'], ['xar', 'application/vnd.xara'], ['xav', 'application/xcap-att+xml'], ['xbap', 'application/x-ms-xbap'], ['xbd', 'application/vnd.fujixerox.docuworks.binder'], ['xbm', 'image/x-xbitmap'], ['xca', 'application/xcap-caps+xml'], ['xcs', 'application/calendar+xml'], ['xdf', 'application/xcap-diff+xml'], ['xdm', 'application/vnd.syncml.dm+xml'], ['xdp', 'application/vnd.adobe.xdp+xml'], ['xdssc', 'application/dssc+xml'], ['xdw', 'application/vnd.fujixerox.docuworks'], ['xel', 'application/xcap-el+xml'], ['xenc', 'application/xenc+xml'], ['xer', 'application/patch-ops-error+xml'], ['xfdf', 'application/vnd.adobe.xfdf'], ['xfdl', 'application/vnd.xfdl'], ['xht', 'application/xhtml+xml'], ['xhtml', 'application/xhtml+xml'], ['xhvml', 'application/xv+xml'], ['xif', 'image/vnd.xiff'], ['xl', 'application/excel'], ['xla', 'application/vnd.ms-excel'], ['xlam', 'application/vnd.ms-excel.addin.macroEnabled.12'], ['xlc', 'application/vnd.ms-excel'], ['xlf', 'application/xliff+xml'], ['xlm', 'application/vnd.ms-excel'], ['xls', 'application/vnd.ms-excel'], ['xlsb', 'application/vnd.ms-excel.sheet.binary.macroEnabled.12'], ['xlsm', 'application/vnd.ms-excel.sheet.macroEnabled.12'], ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'], ['xlt', 'application/vnd.ms-excel'], ['xltm', 'application/vnd.ms-excel.template.macroEnabled.12'], ['xltx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.template'], ['xlw', 'application/vnd.ms-excel'], ['xm', 'audio/xm'], ['xml', 'application/xml'], ['xns', 'application/xcap-ns+xml'], ['xo', 'application/vnd.olpc-sugar'], ['xop', 'application/xop+xml'], ['xpi', 'application/x-xpinstall'], ['xpl', 'application/xproc+xml'], ['xpm', 'image/x-xpixmap'], ['xpr', 'application/vnd.is-xpr'], ['xps', 'application/vnd.ms-xpsdocument'], ['xpw', 'application/vnd.intercon.formnet'], ['xpx', 'application/vnd.intercon.formnet'], ['xsd', 'application/xml'], ['xsl', 'application/xml'], ['xslt', 'application/xslt+xml'], ['xsm', 'application/vnd.syncml+xml'], ['xspf', 'application/xspf+xml'], ['xul', 'application/vnd.mozilla.xul+xml'], ['xvm', 'application/xv+xml'], ['xvml', 'application/xv+xml'], ['xwd', 'image/x-xwindowdump'], ['xyz', 'chemical/x-xyz'], ['xz', 'application/x-xz'], ['yaml', 'text/yaml'], ['yang', 'application/yang'], ['yin', 'application/yin+xml'], ['yml', 'text/yaml'], ['ymp', 'text/x-suse-ymp'], ['z', 'application/x-compress'], ['z1', 'application/x-zmachine'], ['z2', 'application/x-zmachine'], ['z3', 'application/x-zmachine'], ['z4', 'application/x-zmachine'], ['z5', 'application/x-zmachine'], ['z6', 'application/x-zmachine'], ['z7', 'application/x-zmachine'], ['z8', 'application/x-zmachine'], ['zaz', 'application/vnd.zzazz.deck+xml'], ['zip', 'application/zip'], ['zir', 'application/vnd.zul'], ['zirz', 'application/vnd.zul'], ['zmm', 'application/vnd.handheld-entertainment+xml'], ['zsh', 'text/x-scriptzsh']]);\nexport function toFileWithPath(file, path, h) {\n  const f = withMimeType(file);\n  const {\n    webkitRelativePath\n  } = file;\n  const p = typeof path === 'string' ? path\n  // If <input webkitdirectory> is set,\n  // the File will have a {webkitRelativePath} property\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n  : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0 ? webkitRelativePath : `./${file.name}`;\n  if (typeof f.path !== 'string') {\n    // on electron, path is already set to the absolute path\n    setObjProp(f, 'path', p);\n  }\n  if (h !== undefined) {\n    Object.defineProperty(f, 'handle', {\n      value: h,\n      writable: false,\n      configurable: false,\n      enumerable: true\n    });\n  }\n  // Always populate a relative path so that even electron apps have access to a relativePath value\n  setObjProp(f, 'relativePath', p);\n  return f;\n}\nfunction withMimeType(file) {\n  const {\n    name\n  } = file;\n  const hasExtension = name && name.lastIndexOf('.') !== -1;\n  if (hasExtension && !file.type) {\n    const ext = name.split('.').pop().toLowerCase();\n    const type = COMMON_MIME_TYPES.get(ext);\n    if (type) {\n      Object.defineProperty(file, 'type', {\n        value: type,\n        writable: false,\n        configurable: false,\n        enumerable: true\n      });\n    }\n  }\n  return file;\n}\nfunction setObjProp(f, key, value) {\n  Object.defineProperty(f, key, {\n    value,\n    writable: false,\n    configurable: false,\n    enumerable: true\n  });\n}", "map": {"version": 3, "names": ["COMMON_MIME_TYPES", "Map", "toFileWithPath", "file", "path", "h", "f", "withMimeType", "webkitRelativePath", "p", "length", "name", "setObjProp", "undefined", "Object", "defineProperty", "value", "writable", "configurable", "enumerable", "hasExtension", "lastIndexOf", "type", "ext", "split", "pop", "toLowerCase", "get", "key"], "sources": ["C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\node_modules\\file-selector\\src\\file.ts"], "sourcesContent": ["export const COMMON_MIME_TYPES = new Map([\n    // https://github.com/guzzle/psr7/blob/2d9260799e713f1c475d3c5fdc3d6561ff7441b2/src/MimeType.php\n    ['1km', 'application/vnd.1000minds.decision-model+xml'],\n    ['3dml', 'text/vnd.in3d.3dml'],\n    ['3ds', 'image/x-3ds'],\n    ['3g2', 'video/3gpp2'],\n    ['3gp', 'video/3gp'],\n    ['3gpp', 'video/3gpp'],\n    ['3mf', 'model/3mf'],\n    ['7z', 'application/x-7z-compressed'],\n    ['7zip', 'application/x-7z-compressed'],\n    ['123', 'application/vnd.lotus-1-2-3'],\n    ['aab', 'application/x-authorware-bin'],\n    ['aac', 'audio/x-acc'],\n    ['aam', 'application/x-authorware-map'],\n    ['aas', 'application/x-authorware-seg'],\n    ['abw', 'application/x-abiword'],\n    ['ac', 'application/vnd.nokia.n-gage.ac+xml'],\n    ['ac3', 'audio/ac3'],\n    ['acc', 'application/vnd.americandynamics.acc'],\n    ['ace', 'application/x-ace-compressed'],\n    ['acu', 'application/vnd.acucobol'],\n    ['acutc', 'application/vnd.acucorp'],\n    ['adp', 'audio/adpcm'],\n    ['aep', 'application/vnd.audiograph'],\n    ['afm', 'application/x-font-type1'],\n    ['afp', 'application/vnd.ibm.modcap'],\n    ['ahead', 'application/vnd.ahead.space'],\n    ['ai', 'application/pdf'],\n    ['aif', 'audio/x-aiff'],\n    ['aifc', 'audio/x-aiff'],\n    ['aiff', 'audio/x-aiff'],\n    ['air', 'application/vnd.adobe.air-application-installer-package+zip'],\n    ['ait', 'application/vnd.dvb.ait'],\n    ['ami', 'application/vnd.amiga.ami'],\n    ['amr', 'audio/amr'],\n    ['apk', 'application/vnd.android.package-archive'],\n    ['apng', 'image/apng'],\n    ['appcache', 'text/cache-manifest'],\n    ['application', 'application/x-ms-application'],\n    ['apr', 'application/vnd.lotus-approach'],\n    ['arc', 'application/x-freearc'],\n    ['arj', 'application/x-arj'],\n    ['asc', 'application/pgp-signature'],\n    ['asf', 'video/x-ms-asf'],\n    ['asm', 'text/x-asm'],\n    ['aso', 'application/vnd.accpac.simply.aso'],\n    ['asx', 'video/x-ms-asf'],\n    ['atc', 'application/vnd.acucorp'],\n    ['atom', 'application/atom+xml'],\n    ['atomcat', 'application/atomcat+xml'],\n    ['atomdeleted', 'application/atomdeleted+xml'],\n    ['atomsvc', 'application/atomsvc+xml'],\n    ['atx', 'application/vnd.antix.game-component'],\n    ['au', 'audio/x-au'],\n    ['avi', 'video/x-msvideo'],\n    ['avif', 'image/avif'],\n    ['aw', 'application/applixware'],\n    ['azf', 'application/vnd.airzip.filesecure.azf'],\n    ['azs', 'application/vnd.airzip.filesecure.azs'],\n    ['azv', 'image/vnd.airzip.accelerator.azv'],\n    ['azw', 'application/vnd.amazon.ebook'],\n    ['b16', 'image/vnd.pco.b16'],\n    ['bat', 'application/x-msdownload'],\n    ['bcpio', 'application/x-bcpio'],\n    ['bdf', 'application/x-font-bdf'],\n    ['bdm', 'application/vnd.syncml.dm+wbxml'],\n    ['bdoc', 'application/x-bdoc'],\n    ['bed', 'application/vnd.realvnc.bed'],\n    ['bh2', 'application/vnd.fujitsu.oasysprs'],\n    ['bin', 'application/octet-stream'],\n    ['blb', 'application/x-blorb'],\n    ['blorb', 'application/x-blorb'],\n    ['bmi', 'application/vnd.bmi'],\n    ['bmml', 'application/vnd.balsamiq.bmml+xml'],\n    ['bmp', 'image/bmp'],\n    ['book', 'application/vnd.framemaker'],\n    ['box', 'application/vnd.previewsystems.box'],\n    ['boz', 'application/x-bzip2'],\n    ['bpk', 'application/octet-stream'],\n    ['bpmn', 'application/octet-stream'],\n    ['bsp', 'model/vnd.valve.source.compiled-map'],\n    ['btif', 'image/prs.btif'],\n    ['buffer', 'application/octet-stream'],\n    ['bz', 'application/x-bzip'],\n    ['bz2', 'application/x-bzip2'],\n    ['c', 'text/x-c'],\n    ['c4d', 'application/vnd.clonk.c4group'],\n    ['c4f', 'application/vnd.clonk.c4group'],\n    ['c4g', 'application/vnd.clonk.c4group'],\n    ['c4p', 'application/vnd.clonk.c4group'],\n    ['c4u', 'application/vnd.clonk.c4group'],\n    ['c11amc', 'application/vnd.cluetrust.cartomobile-config'],\n    ['c11amz', 'application/vnd.cluetrust.cartomobile-config-pkg'],\n    ['cab', 'application/vnd.ms-cab-compressed'],\n    ['caf', 'audio/x-caf'],\n    ['cap', 'application/vnd.tcpdump.pcap'],\n    ['car', 'application/vnd.curl.car'],\n    ['cat', 'application/vnd.ms-pki.seccat'],\n    ['cb7', 'application/x-cbr'],\n    ['cba', 'application/x-cbr'],\n    ['cbr', 'application/x-cbr'],\n    ['cbt', 'application/x-cbr'],\n    ['cbz', 'application/x-cbr'],\n    ['cc', 'text/x-c'],\n    ['cco', 'application/x-cocoa'],\n    ['cct', 'application/x-director'],\n    ['ccxml', 'application/ccxml+xml'],\n    ['cdbcmsg', 'application/vnd.contact.cmsg'],\n    ['cda', 'application/x-cdf'],\n    ['cdf', 'application/x-netcdf'],\n    ['cdfx', 'application/cdfx+xml'],\n    ['cdkey', 'application/vnd.mediastation.cdkey'],\n    ['cdmia', 'application/cdmi-capability'],\n    ['cdmic', 'application/cdmi-container'],\n    ['cdmid', 'application/cdmi-domain'],\n    ['cdmio', 'application/cdmi-object'],\n    ['cdmiq', 'application/cdmi-queue'],\n    ['cdr', 'application/cdr'],\n    ['cdx', 'chemical/x-cdx'],\n    ['cdxml', 'application/vnd.chemdraw+xml'],\n    ['cdy', 'application/vnd.cinderella'],\n    ['cer', 'application/pkix-cert'],\n    ['cfs', 'application/x-cfs-compressed'],\n    ['cgm', 'image/cgm'],\n    ['chat', 'application/x-chat'],\n    ['chm', 'application/vnd.ms-htmlhelp'],\n    ['chrt', 'application/vnd.kde.kchart'],\n    ['cif', 'chemical/x-cif'],\n    ['cii', 'application/vnd.anser-web-certificate-issue-initiation'],\n    ['cil', 'application/vnd.ms-artgalry'],\n    ['cjs', 'application/node'],\n    ['cla', 'application/vnd.claymore'],\n    ['class', 'application/octet-stream'],\n    ['clkk', 'application/vnd.crick.clicker.keyboard'],\n    ['clkp', 'application/vnd.crick.clicker.palette'],\n    ['clkt', 'application/vnd.crick.clicker.template'],\n    ['clkw', 'application/vnd.crick.clicker.wordbank'],\n    ['clkx', 'application/vnd.crick.clicker'],\n    ['clp', 'application/x-msclip'],\n    ['cmc', 'application/vnd.cosmocaller'],\n    ['cmdf', 'chemical/x-cmdf'],\n    ['cml', 'chemical/x-cml'],\n    ['cmp', 'application/vnd.yellowriver-custom-menu'],\n    ['cmx', 'image/x-cmx'],\n    ['cod', 'application/vnd.rim.cod'],\n    ['coffee', 'text/coffeescript'],\n    ['com', 'application/x-msdownload'],\n    ['conf', 'text/plain'],\n    ['cpio', 'application/x-cpio'],\n    ['cpp', 'text/x-c'],\n    ['cpt', 'application/mac-compactpro'],\n    ['crd', 'application/x-mscardfile'],\n    ['crl', 'application/pkix-crl'],\n    ['crt', 'application/x-x509-ca-cert'],\n    ['crx', 'application/x-chrome-extension'],\n    ['cryptonote', 'application/vnd.rig.cryptonote'],\n    ['csh', 'application/x-csh'],\n    ['csl', 'application/vnd.citationstyles.style+xml'],\n    ['csml', 'chemical/x-csml'],\n    ['csp', 'application/vnd.commonspace'],\n    ['csr', 'application/octet-stream'],\n    ['css', 'text/css'],\n    ['cst', 'application/x-director'],\n    ['csv', 'text/csv'],\n    ['cu', 'application/cu-seeme'],\n    ['curl', 'text/vnd.curl'],\n    ['cww', 'application/prs.cww'],\n    ['cxt', 'application/x-director'],\n    ['cxx', 'text/x-c'],\n    ['dae', 'model/vnd.collada+xml'],\n    ['daf', 'application/vnd.mobius.daf'],\n    ['dart', 'application/vnd.dart'],\n    ['dataless', 'application/vnd.fdsn.seed'],\n    ['davmount', 'application/davmount+xml'],\n    ['dbf', 'application/vnd.dbf'],\n    ['dbk', 'application/docbook+xml'],\n    ['dcr', 'application/x-director'],\n    ['dcurl', 'text/vnd.curl.dcurl'],\n    ['dd2', 'application/vnd.oma.dd2+xml'],\n    ['ddd', 'application/vnd.fujixerox.ddd'],\n    ['ddf', 'application/vnd.syncml.dmddf+xml'],\n    ['dds', 'image/vnd.ms-dds'],\n    ['deb', 'application/x-debian-package'],\n    ['def', 'text/plain'],\n    ['deploy', 'application/octet-stream'],\n    ['der', 'application/x-x509-ca-cert'],\n    ['dfac', 'application/vnd.dreamfactory'],\n    ['dgc', 'application/x-dgc-compressed'],\n    ['dic', 'text/x-c'],\n    ['dir', 'application/x-director'],\n    ['dis', 'application/vnd.mobius.dis'],\n    ['disposition-notification', 'message/disposition-notification'],\n    ['dist', 'application/octet-stream'],\n    ['distz', 'application/octet-stream'],\n    ['djv', 'image/vnd.djvu'],\n    ['djvu', 'image/vnd.djvu'],\n    ['dll', 'application/octet-stream'],\n    ['dmg', 'application/x-apple-diskimage'],\n    ['dmn', 'application/octet-stream'],\n    ['dmp', 'application/vnd.tcpdump.pcap'],\n    ['dms', 'application/octet-stream'],\n    ['dna', 'application/vnd.dna'],\n    ['doc', 'application/msword'],\n    ['docm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\n    ['dot', 'application/msword'],\n    ['dotm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['dotx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.template'],\n    ['dp', 'application/vnd.osgi.dp'],\n    ['dpg', 'application/vnd.dpgraph'],\n    ['dra', 'audio/vnd.dra'],\n    ['drle', 'image/dicom-rle'],\n    ['dsc', 'text/prs.lines.tag'],\n    ['dssc', 'application/dssc+der'],\n    ['dtb', 'application/x-dtbook+xml'],\n    ['dtd', 'application/xml-dtd'],\n    ['dts', 'audio/vnd.dts'],\n    ['dtshd', 'audio/vnd.dts.hd'],\n    ['dump', 'application/octet-stream'],\n    ['dvb', 'video/vnd.dvb.file'],\n    ['dvi', 'application/x-dvi'],\n    ['dwd', 'application/atsc-dwd+xml'],\n    ['dwf', 'model/vnd.dwf'],\n    ['dwg', 'image/vnd.dwg'],\n    ['dxf', 'image/vnd.dxf'],\n    ['dxp', 'application/vnd.spotfire.dxp'],\n    ['dxr', 'application/x-director'],\n    ['ear', 'application/java-archive'],\n    ['ecelp4800', 'audio/vnd.nuera.ecelp4800'],\n    ['ecelp7470', 'audio/vnd.nuera.ecelp7470'],\n    ['ecelp9600', 'audio/vnd.nuera.ecelp9600'],\n    ['ecma', 'application/ecmascript'],\n    ['edm', 'application/vnd.novadigm.edm'],\n    ['edx', 'application/vnd.novadigm.edx'],\n    ['efif', 'application/vnd.picsel'],\n    ['ei6', 'application/vnd.pg.osasli'],\n    ['elc', 'application/octet-stream'],\n    ['emf', 'image/emf'],\n    ['eml', 'message/rfc822'],\n    ['emma', 'application/emma+xml'],\n    ['emotionml', 'application/emotionml+xml'],\n    ['emz', 'application/x-msmetafile'],\n    ['eol', 'audio/vnd.digital-winds'],\n    ['eot', 'application/vnd.ms-fontobject'],\n    ['eps', 'application/postscript'],\n    ['epub', 'application/epub+zip'],\n    ['es', 'application/ecmascript'],\n    ['es3', 'application/vnd.eszigno3+xml'],\n    ['esa', 'application/vnd.osgi.subsystem'],\n    ['esf', 'application/vnd.epson.esf'],\n    ['et3', 'application/vnd.eszigno3+xml'],\n    ['etx', 'text/x-setext'],\n    ['eva', 'application/x-eva'],\n    ['evy', 'application/x-envoy'],\n    ['exe', 'application/octet-stream'],\n    ['exi', 'application/exi'],\n    ['exp', 'application/express'],\n    ['exr', 'image/aces'],\n    ['ext', 'application/vnd.novadigm.ext'],\n    ['ez', 'application/andrew-inset'],\n    ['ez2', 'application/vnd.ezpix-album'],\n    ['ez3', 'application/vnd.ezpix-package'],\n    ['f', 'text/x-fortran'],\n    ['f4v', 'video/mp4'],\n    ['f77', 'text/x-fortran'],\n    ['f90', 'text/x-fortran'],\n    ['fbs', 'image/vnd.fastbidsheet'],\n    ['fcdt', 'application/vnd.adobe.formscentral.fcdt'],\n    ['fcs', 'application/vnd.isac.fcs'],\n    ['fdf', 'application/vnd.fdf'],\n    ['fdt', 'application/fdt+xml'],\n    ['fe_launch', 'application/vnd.denovo.fcselayout-link'],\n    ['fg5', 'application/vnd.fujitsu.oasysgp'],\n    ['fgd', 'application/x-director'],\n    ['fh', 'image/x-freehand'],\n    ['fh4', 'image/x-freehand'],\n    ['fh5', 'image/x-freehand'],\n    ['fh7', 'image/x-freehand'],\n    ['fhc', 'image/x-freehand'],\n    ['fig', 'application/x-xfig'],\n    ['fits', 'image/fits'],\n    ['flac', 'audio/x-flac'],\n    ['fli', 'video/x-fli'],\n    ['flo', 'application/vnd.micrografx.flo'],\n    ['flv', 'video/x-flv'],\n    ['flw', 'application/vnd.kde.kivio'],\n    ['flx', 'text/vnd.fmi.flexstor'],\n    ['fly', 'text/vnd.fly'],\n    ['fm', 'application/vnd.framemaker'],\n    ['fnc', 'application/vnd.frogans.fnc'],\n    ['fo', 'application/vnd.software602.filler.form+xml'],\n    ['for', 'text/x-fortran'],\n    ['fpx', 'image/vnd.fpx'],\n    ['frame', 'application/vnd.framemaker'],\n    ['fsc', 'application/vnd.fsc.weblaunch'],\n    ['fst', 'image/vnd.fst'],\n    ['ftc', 'application/vnd.fluxtime.clip'],\n    ['fti', 'application/vnd.anser-web-funds-transfer-initiation'],\n    ['fvt', 'video/vnd.fvt'],\n    ['fxp', 'application/vnd.adobe.fxp'],\n    ['fxpl', 'application/vnd.adobe.fxp'],\n    ['fzs', 'application/vnd.fuzzysheet'],\n    ['g2w', 'application/vnd.geoplan'],\n    ['g3', 'image/g3fax'],\n    ['g3w', 'application/vnd.geospace'],\n    ['gac', 'application/vnd.groove-account'],\n    ['gam', 'application/x-tads'],\n    ['gbr', 'application/rpki-ghostbusters'],\n    ['gca', 'application/x-gca-compressed'],\n    ['gdl', 'model/vnd.gdl'],\n    ['gdoc', 'application/vnd.google-apps.document'],\n    ['geo', 'application/vnd.dynageo'],\n    ['geojson', 'application/geo+json'],\n    ['gex', 'application/vnd.geometry-explorer'],\n    ['ggb', 'application/vnd.geogebra.file'],\n    ['ggt', 'application/vnd.geogebra.tool'],\n    ['ghf', 'application/vnd.groove-help'],\n    ['gif', 'image/gif'],\n    ['gim', 'application/vnd.groove-identity-message'],\n    ['glb', 'model/gltf-binary'],\n    ['gltf', 'model/gltf+json'],\n    ['gml', 'application/gml+xml'],\n    ['gmx', 'application/vnd.gmx'],\n    ['gnumeric', 'application/x-gnumeric'],\n    ['gpg', 'application/gpg-keys'],\n    ['gph', 'application/vnd.flographit'],\n    ['gpx', 'application/gpx+xml'],\n    ['gqf', 'application/vnd.grafeq'],\n    ['gqs', 'application/vnd.grafeq'],\n    ['gram', 'application/srgs'],\n    ['gramps', 'application/x-gramps-xml'],\n    ['gre', 'application/vnd.geometry-explorer'],\n    ['grv', 'application/vnd.groove-injector'],\n    ['grxml', 'application/srgs+xml'],\n    ['gsf', 'application/x-font-ghostscript'],\n    ['gsheet', 'application/vnd.google-apps.spreadsheet'],\n    ['gslides', 'application/vnd.google-apps.presentation'],\n    ['gtar', 'application/x-gtar'],\n    ['gtm', 'application/vnd.groove-tool-message'],\n    ['gtw', 'model/vnd.gtw'],\n    ['gv', 'text/vnd.graphviz'],\n    ['gxf', 'application/gxf'],\n    ['gxt', 'application/vnd.geonext'],\n    ['gz', 'application/gzip'],\n    ['gzip', 'application/gzip'],\n    ['h', 'text/x-c'],\n    ['h261', 'video/h261'],\n    ['h263', 'video/h263'],\n    ['h264', 'video/h264'],\n    ['hal', 'application/vnd.hal+xml'],\n    ['hbci', 'application/vnd.hbci'],\n    ['hbs', 'text/x-handlebars-template'],\n    ['hdd', 'application/x-virtualbox-hdd'],\n    ['hdf', 'application/x-hdf'],\n    ['heic', 'image/heic'],\n    ['heics', 'image/heic-sequence'],\n    ['heif', 'image/heif'],\n    ['heifs', 'image/heif-sequence'],\n    ['hej2', 'image/hej2k'],\n    ['held', 'application/atsc-held+xml'],\n    ['hh', 'text/x-c'],\n    ['hjson', 'application/hjson'],\n    ['hlp', 'application/winhlp'],\n    ['hpgl', 'application/vnd.hp-hpgl'],\n    ['hpid', 'application/vnd.hp-hpid'],\n    ['hps', 'application/vnd.hp-hps'],\n    ['hqx', 'application/mac-binhex40'],\n    ['hsj2', 'image/hsj2'],\n    ['htc', 'text/x-component'],\n    ['htke', 'application/vnd.kenameaapp'],\n    ['htm', 'text/html'],\n    ['html', 'text/html'],\n    ['hvd', 'application/vnd.yamaha.hv-dic'],\n    ['hvp', 'application/vnd.yamaha.hv-voice'],\n    ['hvs', 'application/vnd.yamaha.hv-script'],\n    ['i2g', 'application/vnd.intergeo'],\n    ['icc', 'application/vnd.iccprofile'],\n    ['ice', 'x-conference/x-cooltalk'],\n    ['icm', 'application/vnd.iccprofile'],\n    ['ico', 'image/x-icon'],\n    ['ics', 'text/calendar'],\n    ['ief', 'image/ief'],\n    ['ifb', 'text/calendar'],\n    ['ifm', 'application/vnd.shana.informed.formdata'],\n    ['iges', 'model/iges'],\n    ['igl', 'application/vnd.igloader'],\n    ['igm', 'application/vnd.insors.igm'],\n    ['igs', 'model/iges'],\n    ['igx', 'application/vnd.micrografx.igx'],\n    ['iif', 'application/vnd.shana.informed.interchange'],\n    ['img', 'application/octet-stream'],\n    ['imp', 'application/vnd.accpac.simply.imp'],\n    ['ims', 'application/vnd.ms-ims'],\n    ['in', 'text/plain'],\n    ['ini', 'text/plain'],\n    ['ink', 'application/inkml+xml'],\n    ['inkml', 'application/inkml+xml'],\n    ['install', 'application/x-install-instructions'],\n    ['iota', 'application/vnd.astraea-software.iota'],\n    ['ipfix', 'application/ipfix'],\n    ['ipk', 'application/vnd.shana.informed.package'],\n    ['irm', 'application/vnd.ibm.rights-management'],\n    ['irp', 'application/vnd.irepository.package+xml'],\n    ['iso', 'application/x-iso9660-image'],\n    ['itp', 'application/vnd.shana.informed.formtemplate'],\n    ['its', 'application/its+xml'],\n    ['ivp', 'application/vnd.immervision-ivp'],\n    ['ivu', 'application/vnd.immervision-ivu'],\n    ['jad', 'text/vnd.sun.j2me.app-descriptor'],\n    ['jade', 'text/jade'],\n    ['jam', 'application/vnd.jam'],\n    ['jar', 'application/java-archive'],\n    ['jardiff', 'application/x-java-archive-diff'],\n    ['java', 'text/x-java-source'],\n    ['jhc', 'image/jphc'],\n    ['jisp', 'application/vnd.jisp'],\n    ['jls', 'image/jls'],\n    ['jlt', 'application/vnd.hp-jlyt'],\n    ['jng', 'image/x-jng'],\n    ['jnlp', 'application/x-java-jnlp-file'],\n    ['joda', 'application/vnd.joost.joda-archive'],\n    ['jp2', 'image/jp2'],\n    ['jpe', 'image/jpeg'],\n    ['jpeg', 'image/jpeg'],\n    ['jpf', 'image/jpx'],\n    ['jpg', 'image/jpeg'],\n    ['jpg2', 'image/jp2'],\n    ['jpgm', 'video/jpm'],\n    ['jpgv', 'video/jpeg'],\n    ['jph', 'image/jph'],\n    ['jpm', 'video/jpm'],\n    ['jpx', 'image/jpx'],\n    ['js', 'application/javascript'],\n    ['json', 'application/json'],\n    ['json5', 'application/json5'],\n    ['jsonld', 'application/ld+json'],\n    // https://jsonlines.org/\n    ['jsonl', 'application/jsonl'],\n    ['jsonml', 'application/jsonml+json'],\n    ['jsx', 'text/jsx'],\n    ['jxr', 'image/jxr'],\n    ['jxra', 'image/jxra'],\n    ['jxrs', 'image/jxrs'],\n    ['jxs', 'image/jxs'],\n    ['jxsc', 'image/jxsc'],\n    ['jxsi', 'image/jxsi'],\n    ['jxss', 'image/jxss'],\n    ['kar', 'audio/midi'],\n    ['karbon', 'application/vnd.kde.karbon'],\n    ['kdb', 'application/octet-stream'],\n    ['kdbx', 'application/x-keepass2'],\n    ['key', 'application/x-iwork-keynote-sffkey'],\n    ['kfo', 'application/vnd.kde.kformula'],\n    ['kia', 'application/vnd.kidspiration'],\n    ['kml', 'application/vnd.google-earth.kml+xml'],\n    ['kmz', 'application/vnd.google-earth.kmz'],\n    ['kne', 'application/vnd.kinar'],\n    ['knp', 'application/vnd.kinar'],\n    ['kon', 'application/vnd.kde.kontour'],\n    ['kpr', 'application/vnd.kde.kpresenter'],\n    ['kpt', 'application/vnd.kde.kpresenter'],\n    ['kpxx', 'application/vnd.ds-keypoint'],\n    ['ksp', 'application/vnd.kde.kspread'],\n    ['ktr', 'application/vnd.kahootz'],\n    ['ktx', 'image/ktx'],\n    ['ktx2', 'image/ktx2'],\n    ['ktz', 'application/vnd.kahootz'],\n    ['kwd', 'application/vnd.kde.kword'],\n    ['kwt', 'application/vnd.kde.kword'],\n    ['lasxml', 'application/vnd.las.las+xml'],\n    ['latex', 'application/x-latex'],\n    ['lbd', 'application/vnd.llamagraphics.life-balance.desktop'],\n    ['lbe', 'application/vnd.llamagraphics.life-balance.exchange+xml'],\n    ['les', 'application/vnd.hhe.lesson-player'],\n    ['less', 'text/less'],\n    ['lgr', 'application/lgr+xml'],\n    ['lha', 'application/octet-stream'],\n    ['link66', 'application/vnd.route66.link66+xml'],\n    ['list', 'text/plain'],\n    ['list3820', 'application/vnd.ibm.modcap'],\n    ['listafp', 'application/vnd.ibm.modcap'],\n    ['litcoffee', 'text/coffeescript'],\n    ['lnk', 'application/x-ms-shortcut'],\n    ['log', 'text/plain'],\n    ['lostxml', 'application/lost+xml'],\n    ['lrf', 'application/octet-stream'],\n    ['lrm', 'application/vnd.ms-lrm'],\n    ['ltf', 'application/vnd.frogans.ltf'],\n    ['lua', 'text/x-lua'],\n    ['luac', 'application/x-lua-bytecode'],\n    ['lvp', 'audio/vnd.lucent.voice'],\n    ['lwp', 'application/vnd.lotus-wordpro'],\n    ['lzh', 'application/octet-stream'],\n    ['m1v', 'video/mpeg'],\n    ['m2a', 'audio/mpeg'],\n    ['m2v', 'video/mpeg'],\n    ['m3a', 'audio/mpeg'],\n    ['m3u', 'text/plain'],\n    ['m3u8', 'application/vnd.apple.mpegurl'],\n    ['m4a', 'audio/x-m4a'],\n    ['m4p', 'application/mp4'],\n    ['m4s', 'video/iso.segment'],\n    ['m4u', 'application/vnd.mpegurl'],\n    ['m4v', 'video/x-m4v'],\n    ['m13', 'application/x-msmediaview'],\n    ['m14', 'application/x-msmediaview'],\n    ['m21', 'application/mp21'],\n    ['ma', 'application/mathematica'],\n    ['mads', 'application/mads+xml'],\n    ['maei', 'application/mmt-aei+xml'],\n    ['mag', 'application/vnd.ecowin.chart'],\n    ['maker', 'application/vnd.framemaker'],\n    ['man', 'text/troff'],\n    ['manifest', 'text/cache-manifest'],\n    ['map', 'application/json'],\n    ['mar', 'application/octet-stream'],\n    ['markdown', 'text/markdown'],\n    ['mathml', 'application/mathml+xml'],\n    ['mb', 'application/mathematica'],\n    ['mbk', 'application/vnd.mobius.mbk'],\n    ['mbox', 'application/mbox'],\n    ['mc1', 'application/vnd.medcalcdata'],\n    ['mcd', 'application/vnd.mcd'],\n    ['mcurl', 'text/vnd.curl.mcurl'],\n    ['md', 'text/markdown'],\n    ['mdb', 'application/x-msaccess'],\n    ['mdi', 'image/vnd.ms-modi'],\n    ['mdx', 'text/mdx'],\n    ['me', 'text/troff'],\n    ['mesh', 'model/mesh'],\n    ['meta4', 'application/metalink4+xml'],\n    ['metalink', 'application/metalink+xml'],\n    ['mets', 'application/mets+xml'],\n    ['mfm', 'application/vnd.mfmp'],\n    ['mft', 'application/rpki-manifest'],\n    ['mgp', 'application/vnd.osgeo.mapguide.package'],\n    ['mgz', 'application/vnd.proteus.magazine'],\n    ['mid', 'audio/midi'],\n    ['midi', 'audio/midi'],\n    ['mie', 'application/x-mie'],\n    ['mif', 'application/vnd.mif'],\n    ['mime', 'message/rfc822'],\n    ['mj2', 'video/mj2'],\n    ['mjp2', 'video/mj2'],\n    ['mjs', 'application/javascript'],\n    ['mk3d', 'video/x-matroska'],\n    ['mka', 'audio/x-matroska'],\n    ['mkd', 'text/x-markdown'],\n    ['mks', 'video/x-matroska'],\n    ['mkv', 'video/x-matroska'],\n    ['mlp', 'application/vnd.dolby.mlp'],\n    ['mmd', 'application/vnd.chipnuts.karaoke-mmd'],\n    ['mmf', 'application/vnd.smaf'],\n    ['mml', 'text/mathml'],\n    ['mmr', 'image/vnd.fujixerox.edmics-mmr'],\n    ['mng', 'video/x-mng'],\n    ['mny', 'application/x-msmoney'],\n    ['mobi', 'application/x-mobipocket-ebook'],\n    ['mods', 'application/mods+xml'],\n    ['mov', 'video/quicktime'],\n    ['movie', 'video/x-sgi-movie'],\n    ['mp2', 'audio/mpeg'],\n    ['mp2a', 'audio/mpeg'],\n    ['mp3', 'audio/mpeg'],\n    ['mp4', 'video/mp4'],\n    ['mp4a', 'audio/mp4'],\n    ['mp4s', 'application/mp4'],\n    ['mp4v', 'video/mp4'],\n    ['mp21', 'application/mp21'],\n    ['mpc', 'application/vnd.mophun.certificate'],\n    ['mpd', 'application/dash+xml'],\n    ['mpe', 'video/mpeg'],\n    ['mpeg', 'video/mpeg'],\n    ['mpg', 'video/mpeg'],\n    ['mpg4', 'video/mp4'],\n    ['mpga', 'audio/mpeg'],\n    ['mpkg', 'application/vnd.apple.installer+xml'],\n    ['mpm', 'application/vnd.blueice.multipass'],\n    ['mpn', 'application/vnd.mophun.application'],\n    ['mpp', 'application/vnd.ms-project'],\n    ['mpt', 'application/vnd.ms-project'],\n    ['mpy', 'application/vnd.ibm.minipay'],\n    ['mqy', 'application/vnd.mobius.mqy'],\n    ['mrc', 'application/marc'],\n    ['mrcx', 'application/marcxml+xml'],\n    ['ms', 'text/troff'],\n    ['mscml', 'application/mediaservercontrol+xml'],\n    ['mseed', 'application/vnd.fdsn.mseed'],\n    ['mseq', 'application/vnd.mseq'],\n    ['msf', 'application/vnd.epson.msf'],\n    ['msg', 'application/vnd.ms-outlook'],\n    ['msh', 'model/mesh'],\n    ['msi', 'application/x-msdownload'],\n    ['msl', 'application/vnd.mobius.msl'],\n    ['msm', 'application/octet-stream'],\n    ['msp', 'application/octet-stream'],\n    ['msty', 'application/vnd.muvee.style'],\n    ['mtl', 'model/mtl'],\n    ['mts', 'model/vnd.mts'],\n    ['mus', 'application/vnd.musician'],\n    ['musd', 'application/mmt-usd+xml'],\n    ['musicxml', 'application/vnd.recordare.musicxml+xml'],\n    ['mvb', 'application/x-msmediaview'],\n    ['mvt', 'application/vnd.mapbox-vector-tile'],\n    ['mwf', 'application/vnd.mfer'],\n    ['mxf', 'application/mxf'],\n    ['mxl', 'application/vnd.recordare.musicxml'],\n    ['mxmf', 'audio/mobile-xmf'],\n    ['mxml', 'application/xv+xml'],\n    ['mxs', 'application/vnd.triscape.mxs'],\n    ['mxu', 'video/vnd.mpegurl'],\n    ['n-gage', 'application/vnd.nokia.n-gage.symbian.install'],\n    ['n3', 'text/n3'],\n    ['nb', 'application/mathematica'],\n    ['nbp', 'application/vnd.wolfram.player'],\n    ['nc', 'application/x-netcdf'],\n    ['ncx', 'application/x-dtbncx+xml'],\n    ['nfo', 'text/x-nfo'],\n    ['ngdat', 'application/vnd.nokia.n-gage.data'],\n    ['nitf', 'application/vnd.nitf'],\n    ['nlu', 'application/vnd.neurolanguage.nlu'],\n    ['nml', 'application/vnd.enliven'],\n    ['nnd', 'application/vnd.noblenet-directory'],\n    ['nns', 'application/vnd.noblenet-sealer'],\n    ['nnw', 'application/vnd.noblenet-web'],\n    ['npx', 'image/vnd.net-fpx'],\n    ['nq', 'application/n-quads'],\n    ['nsc', 'application/x-conference'],\n    ['nsf', 'application/vnd.lotus-notes'],\n    ['nt', 'application/n-triples'],\n    ['ntf', 'application/vnd.nitf'],\n    ['numbers', 'application/x-iwork-numbers-sffnumbers'],\n    ['nzb', 'application/x-nzb'],\n    ['oa2', 'application/vnd.fujitsu.oasys2'],\n    ['oa3', 'application/vnd.fujitsu.oasys3'],\n    ['oas', 'application/vnd.fujitsu.oasys'],\n    ['obd', 'application/x-msbinder'],\n    ['obgx', 'application/vnd.openblox.game+xml'],\n    ['obj', 'model/obj'],\n    ['oda', 'application/oda'],\n    ['odb', 'application/vnd.oasis.opendocument.database'],\n    ['odc', 'application/vnd.oasis.opendocument.chart'],\n    ['odf', 'application/vnd.oasis.opendocument.formula'],\n    ['odft', 'application/vnd.oasis.opendocument.formula-template'],\n    ['odg', 'application/vnd.oasis.opendocument.graphics'],\n    ['odi', 'application/vnd.oasis.opendocument.image'],\n    ['odm', 'application/vnd.oasis.opendocument.text-master'],\n    ['odp', 'application/vnd.oasis.opendocument.presentation'],\n    ['ods', 'application/vnd.oasis.opendocument.spreadsheet'],\n    ['odt', 'application/vnd.oasis.opendocument.text'],\n    ['oga', 'audio/ogg'],\n    ['ogex', 'model/vnd.opengex'],\n    ['ogg', 'audio/ogg'],\n    ['ogv', 'video/ogg'],\n    ['ogx', 'application/ogg'],\n    ['omdoc', 'application/omdoc+xml'],\n    ['onepkg', 'application/onenote'],\n    ['onetmp', 'application/onenote'],\n    ['onetoc', 'application/onenote'],\n    ['onetoc2', 'application/onenote'],\n    ['opf', 'application/oebps-package+xml'],\n    ['opml', 'text/x-opml'],\n    ['oprc', 'application/vnd.palm'],\n    ['opus', 'audio/ogg'],\n    ['org', 'text/x-org'],\n    ['osf', 'application/vnd.yamaha.openscoreformat'],\n    ['osfpvg', 'application/vnd.yamaha.openscoreformat.osfpvg+xml'],\n    ['osm', 'application/vnd.openstreetmap.data+xml'],\n    ['otc', 'application/vnd.oasis.opendocument.chart-template'],\n    ['otf', 'font/otf'],\n    ['otg', 'application/vnd.oasis.opendocument.graphics-template'],\n    ['oth', 'application/vnd.oasis.opendocument.text-web'],\n    ['oti', 'application/vnd.oasis.opendocument.image-template'],\n    ['otp', 'application/vnd.oasis.opendocument.presentation-template'],\n    ['ots', 'application/vnd.oasis.opendocument.spreadsheet-template'],\n    ['ott', 'application/vnd.oasis.opendocument.text-template'],\n    ['ova', 'application/x-virtualbox-ova'],\n    ['ovf', 'application/x-virtualbox-ovf'],\n    ['owl', 'application/rdf+xml'],\n    ['oxps', 'application/oxps'],\n    ['oxt', 'application/vnd.openofficeorg.extension'],\n    ['p', 'text/x-pascal'],\n    ['p7a', 'application/x-pkcs7-signature'],\n    ['p7b', 'application/x-pkcs7-certificates'],\n    ['p7c', 'application/pkcs7-mime'],\n    ['p7m', 'application/pkcs7-mime'],\n    ['p7r', 'application/x-pkcs7-certreqresp'],\n    ['p7s', 'application/pkcs7-signature'],\n    ['p8', 'application/pkcs8'],\n    ['p10', 'application/x-pkcs10'],\n    ['p12', 'application/x-pkcs12'],\n    ['pac', 'application/x-ns-proxy-autoconfig'],\n    ['pages', 'application/x-iwork-pages-sffpages'],\n    ['pas', 'text/x-pascal'],\n    ['paw', 'application/vnd.pawaafile'],\n    ['pbd', 'application/vnd.powerbuilder6'],\n    ['pbm', 'image/x-portable-bitmap'],\n    ['pcap', 'application/vnd.tcpdump.pcap'],\n    ['pcf', 'application/x-font-pcf'],\n    ['pcl', 'application/vnd.hp-pcl'],\n    ['pclxl', 'application/vnd.hp-pclxl'],\n    ['pct', 'image/x-pict'],\n    ['pcurl', 'application/vnd.curl.pcurl'],\n    ['pcx', 'image/x-pcx'],\n    ['pdb', 'application/x-pilot'],\n    ['pde', 'text/x-processing'],\n    ['pdf', 'application/pdf'],\n    ['pem', 'application/x-x509-user-cert'],\n    ['pfa', 'application/x-font-type1'],\n    ['pfb', 'application/x-font-type1'],\n    ['pfm', 'application/x-font-type1'],\n    ['pfr', 'application/font-tdpfr'],\n    ['pfx', 'application/x-pkcs12'],\n    ['pgm', 'image/x-portable-graymap'],\n    ['pgn', 'application/x-chess-pgn'],\n    ['pgp', 'application/pgp'],\n    ['php', 'application/x-httpd-php'],\n    ['php3', 'application/x-httpd-php'],\n    ['php4', 'application/x-httpd-php'],\n    ['phps', 'application/x-httpd-php-source'],\n    ['phtml', 'application/x-httpd-php'],\n    ['pic', 'image/x-pict'],\n    ['pkg', 'application/octet-stream'],\n    ['pki', 'application/pkixcmp'],\n    ['pkipath', 'application/pkix-pkipath'],\n    ['pkpass', 'application/vnd.apple.pkpass'],\n    ['pl', 'application/x-perl'],\n    ['plb', 'application/vnd.3gpp.pic-bw-large'],\n    ['plc', 'application/vnd.mobius.plc'],\n    ['plf', 'application/vnd.pocketlearn'],\n    ['pls', 'application/pls+xml'],\n    ['pm', 'application/x-perl'],\n    ['pml', 'application/vnd.ctc-posml'],\n    ['png', 'image/png'],\n    ['pnm', 'image/x-portable-anymap'],\n    ['portpkg', 'application/vnd.macports.portpkg'],\n    ['pot', 'application/vnd.ms-powerpoint'],\n    ['potm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['potx', 'application/vnd.openxmlformats-officedocument.presentationml.template'],\n    ['ppa', 'application/vnd.ms-powerpoint'],\n    ['ppam', 'application/vnd.ms-powerpoint.addin.macroEnabled.12'],\n    ['ppd', 'application/vnd.cups-ppd'],\n    ['ppm', 'image/x-portable-pixmap'],\n    ['pps', 'application/vnd.ms-powerpoint'],\n    ['ppsm', 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12'],\n    ['ppsx', 'application/vnd.openxmlformats-officedocument.presentationml.slideshow'],\n    ['ppt', 'application/powerpoint'],\n    ['pptm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],\n    ['pqa', 'application/vnd.palm'],\n    ['prc', 'application/x-pilot'],\n    ['pre', 'application/vnd.lotus-freelance'],\n    ['prf', 'application/pics-rules'],\n    ['provx', 'application/provenance+xml'],\n    ['ps', 'application/postscript'],\n    ['psb', 'application/vnd.3gpp.pic-bw-small'],\n    ['psd', 'application/x-photoshop'],\n    ['psf', 'application/x-font-linux-psf'],\n    ['pskcxml', 'application/pskc+xml'],\n    ['pti', 'image/prs.pti'],\n    ['ptid', 'application/vnd.pvi.ptid1'],\n    ['pub', 'application/x-mspublisher'],\n    ['pvb', 'application/vnd.3gpp.pic-bw-var'],\n    ['pwn', 'application/vnd.3m.post-it-notes'],\n    ['pya', 'audio/vnd.ms-playready.media.pya'],\n    ['pyv', 'video/vnd.ms-playready.media.pyv'],\n    ['qam', 'application/vnd.epson.quickanime'],\n    ['qbo', 'application/vnd.intu.qbo'],\n    ['qfx', 'application/vnd.intu.qfx'],\n    ['qps', 'application/vnd.publishare-delta-tree'],\n    ['qt', 'video/quicktime'],\n    ['qwd', 'application/vnd.quark.quarkxpress'],\n    ['qwt', 'application/vnd.quark.quarkxpress'],\n    ['qxb', 'application/vnd.quark.quarkxpress'],\n    ['qxd', 'application/vnd.quark.quarkxpress'],\n    ['qxl', 'application/vnd.quark.quarkxpress'],\n    ['qxt', 'application/vnd.quark.quarkxpress'],\n    ['ra', 'audio/x-realaudio'],\n    ['ram', 'audio/x-pn-realaudio'],\n    ['raml', 'application/raml+yaml'],\n    ['rapd', 'application/route-apd+xml'],\n    ['rar', 'application/x-rar'],\n    ['ras', 'image/x-cmu-raster'],\n    ['rcprofile', 'application/vnd.ipunplugged.rcprofile'],\n    ['rdf', 'application/rdf+xml'],\n    ['rdz', 'application/vnd.data-vision.rdz'],\n    ['relo', 'application/p2p-overlay+xml'],\n    ['rep', 'application/vnd.businessobjects'],\n    ['res', 'application/x-dtbresource+xml'],\n    ['rgb', 'image/x-rgb'],\n    ['rif', 'application/reginfo+xml'],\n    ['rip', 'audio/vnd.rip'],\n    ['ris', 'application/x-research-info-systems'],\n    ['rl', 'application/resource-lists+xml'],\n    ['rlc', 'image/vnd.fujixerox.edmics-rlc'],\n    ['rld', 'application/resource-lists-diff+xml'],\n    ['rm', 'audio/x-pn-realaudio'],\n    ['rmi', 'audio/midi'],\n    ['rmp', 'audio/x-pn-realaudio-plugin'],\n    ['rms', 'application/vnd.jcp.javame.midlet-rms'],\n    ['rmvb', 'application/vnd.rn-realmedia-vbr'],\n    ['rnc', 'application/relax-ng-compact-syntax'],\n    ['rng', 'application/xml'],\n    ['roa', 'application/rpki-roa'],\n    ['roff', 'text/troff'],\n    ['rp9', 'application/vnd.cloanto.rp9'],\n    ['rpm', 'audio/x-pn-realaudio-plugin'],\n    ['rpss', 'application/vnd.nokia.radio-presets'],\n    ['rpst', 'application/vnd.nokia.radio-preset'],\n    ['rq', 'application/sparql-query'],\n    ['rs', 'application/rls-services+xml'],\n    ['rsa', 'application/x-pkcs7'],\n    ['rsat', 'application/atsc-rsat+xml'],\n    ['rsd', 'application/rsd+xml'],\n    ['rsheet', 'application/urc-ressheet+xml'],\n    ['rss', 'application/rss+xml'],\n    ['rtf', 'text/rtf'],\n    ['rtx', 'text/richtext'],\n    ['run', 'application/x-makeself'],\n    ['rusd', 'application/route-usd+xml'],\n    ['rv', 'video/vnd.rn-realvideo'],\n    ['s', 'text/x-asm'],\n    ['s3m', 'audio/s3m'],\n    ['saf', 'application/vnd.yamaha.smaf-audio'],\n    ['sass', 'text/x-sass'],\n    ['sbml', 'application/sbml+xml'],\n    ['sc', 'application/vnd.ibm.secure-container'],\n    ['scd', 'application/x-msschedule'],\n    ['scm', 'application/vnd.lotus-screencam'],\n    ['scq', 'application/scvp-cv-request'],\n    ['scs', 'application/scvp-cv-response'],\n    ['scss', 'text/x-scss'],\n    ['scurl', 'text/vnd.curl.scurl'],\n    ['sda', 'application/vnd.stardivision.draw'],\n    ['sdc', 'application/vnd.stardivision.calc'],\n    ['sdd', 'application/vnd.stardivision.impress'],\n    ['sdkd', 'application/vnd.solent.sdkm+xml'],\n    ['sdkm', 'application/vnd.solent.sdkm+xml'],\n    ['sdp', 'application/sdp'],\n    ['sdw', 'application/vnd.stardivision.writer'],\n    ['sea', 'application/octet-stream'],\n    ['see', 'application/vnd.seemail'],\n    ['seed', 'application/vnd.fdsn.seed'],\n    ['sema', 'application/vnd.sema'],\n    ['semd', 'application/vnd.semd'],\n    ['semf', 'application/vnd.semf'],\n    ['senmlx', 'application/senml+xml'],\n    ['sensmlx', 'application/sensml+xml'],\n    ['ser', 'application/java-serialized-object'],\n    ['setpay', 'application/set-payment-initiation'],\n    ['setreg', 'application/set-registration-initiation'],\n    ['sfd-hdstx', 'application/vnd.hydrostatix.sof-data'],\n    ['sfs', 'application/vnd.spotfire.sfs'],\n    ['sfv', 'text/x-sfv'],\n    ['sgi', 'image/sgi'],\n    ['sgl', 'application/vnd.stardivision.writer-global'],\n    ['sgm', 'text/sgml'],\n    ['sgml', 'text/sgml'],\n    ['sh', 'application/x-sh'],\n    ['shar', 'application/x-shar'],\n    ['shex', 'text/shex'],\n    ['shf', 'application/shf+xml'],\n    ['shtml', 'text/html'],\n    ['sid', 'image/x-mrsid-image'],\n    ['sieve', 'application/sieve'],\n    ['sig', 'application/pgp-signature'],\n    ['sil', 'audio/silk'],\n    ['silo', 'model/mesh'],\n    ['sis', 'application/vnd.symbian.install'],\n    ['sisx', 'application/vnd.symbian.install'],\n    ['sit', 'application/x-stuffit'],\n    ['sitx', 'application/x-stuffitx'],\n    ['siv', 'application/sieve'],\n    ['skd', 'application/vnd.koan'],\n    ['skm', 'application/vnd.koan'],\n    ['skp', 'application/vnd.koan'],\n    ['skt', 'application/vnd.koan'],\n    ['sldm', 'application/vnd.ms-powerpoint.slide.macroenabled.12'],\n    ['sldx', 'application/vnd.openxmlformats-officedocument.presentationml.slide'],\n    ['slim', 'text/slim'],\n    ['slm', 'text/slim'],\n    ['sls', 'application/route-s-tsid+xml'],\n    ['slt', 'application/vnd.epson.salt'],\n    ['sm', 'application/vnd.stepmania.stepchart'],\n    ['smf', 'application/vnd.stardivision.math'],\n    ['smi', 'application/smil'],\n    ['smil', 'application/smil'],\n    ['smv', 'video/x-smv'],\n    ['smzip', 'application/vnd.stepmania.package'],\n    ['snd', 'audio/basic'],\n    ['snf', 'application/x-font-snf'],\n    ['so', 'application/octet-stream'],\n    ['spc', 'application/x-pkcs7-certificates'],\n    ['spdx', 'text/spdx'],\n    ['spf', 'application/vnd.yamaha.smaf-phrase'],\n    ['spl', 'application/x-futuresplash'],\n    ['spot', 'text/vnd.in3d.spot'],\n    ['spp', 'application/scvp-vp-response'],\n    ['spq', 'application/scvp-vp-request'],\n    ['spx', 'audio/ogg'],\n    ['sql', 'application/x-sql'],\n    ['src', 'application/x-wais-source'],\n    ['srt', 'application/x-subrip'],\n    ['sru', 'application/sru+xml'],\n    ['srx', 'application/sparql-results+xml'],\n    ['ssdl', 'application/ssdl+xml'],\n    ['sse', 'application/vnd.kodak-descriptor'],\n    ['ssf', 'application/vnd.epson.ssf'],\n    ['ssml', 'application/ssml+xml'],\n    ['sst', 'application/octet-stream'],\n    ['st', 'application/vnd.sailingtracker.track'],\n    ['stc', 'application/vnd.sun.xml.calc.template'],\n    ['std', 'application/vnd.sun.xml.draw.template'],\n    ['stf', 'application/vnd.wt.stf'],\n    ['sti', 'application/vnd.sun.xml.impress.template'],\n    ['stk', 'application/hyperstudio'],\n    ['stl', 'model/stl'],\n    ['stpx', 'model/step+xml'],\n    ['stpxz', 'model/step-xml+zip'],\n    ['stpz', 'model/step+zip'],\n    ['str', 'application/vnd.pg.format'],\n    ['stw', 'application/vnd.sun.xml.writer.template'],\n    ['styl', 'text/stylus'],\n    ['stylus', 'text/stylus'],\n    ['sub', 'text/vnd.dvb.subtitle'],\n    ['sus', 'application/vnd.sus-calendar'],\n    ['susp', 'application/vnd.sus-calendar'],\n    ['sv4cpio', 'application/x-sv4cpio'],\n    ['sv4crc', 'application/x-sv4crc'],\n    ['svc', 'application/vnd.dvb.service'],\n    ['svd', 'application/vnd.svd'],\n    ['svg', 'image/svg+xml'],\n    ['svgz', 'image/svg+xml'],\n    ['swa', 'application/x-director'],\n    ['swf', 'application/x-shockwave-flash'],\n    ['swi', 'application/vnd.aristanetworks.swi'],\n    ['swidtag', 'application/swid+xml'],\n    ['sxc', 'application/vnd.sun.xml.calc'],\n    ['sxd', 'application/vnd.sun.xml.draw'],\n    ['sxg', 'application/vnd.sun.xml.writer.global'],\n    ['sxi', 'application/vnd.sun.xml.impress'],\n    ['sxm', 'application/vnd.sun.xml.math'],\n    ['sxw', 'application/vnd.sun.xml.writer'],\n    ['t', 'text/troff'],\n    ['t3', 'application/x-t3vm-image'],\n    ['t38', 'image/t38'],\n    ['taglet', 'application/vnd.mynfc'],\n    ['tao', 'application/vnd.tao.intent-module-archive'],\n    ['tap', 'image/vnd.tencent.tap'],\n    ['tar', 'application/x-tar'],\n    ['tcap', 'application/vnd.3gpp2.tcap'],\n    ['tcl', 'application/x-tcl'],\n    ['td', 'application/urc-targetdesc+xml'],\n    ['teacher', 'application/vnd.smart.teacher'],\n    ['tei', 'application/tei+xml'],\n    ['teicorpus', 'application/tei+xml'],\n    ['tex', 'application/x-tex'],\n    ['texi', 'application/x-texinfo'],\n    ['texinfo', 'application/x-texinfo'],\n    ['text', 'text/plain'],\n    ['tfi', 'application/thraud+xml'],\n    ['tfm', 'application/x-tex-tfm'],\n    ['tfx', 'image/tiff-fx'],\n    ['tga', 'image/x-tga'],\n    ['tgz', 'application/x-tar'],\n    ['thmx', 'application/vnd.ms-officetheme'],\n    ['tif', 'image/tiff'],\n    ['tiff', 'image/tiff'],\n    ['tk', 'application/x-tcl'],\n    ['tmo', 'application/vnd.tmobile-livetv'],\n    ['toml', 'application/toml'],\n    ['torrent', 'application/x-bittorrent'],\n    ['tpl', 'application/vnd.groove-tool-template'],\n    ['tpt', 'application/vnd.trid.tpt'],\n    ['tr', 'text/troff'],\n    ['tra', 'application/vnd.trueapp'],\n    ['trig', 'application/trig'],\n    ['trm', 'application/x-msterminal'],\n    ['ts', 'video/mp2t'],\n    ['tsd', 'application/timestamped-data'],\n    ['tsv', 'text/tab-separated-values'],\n    ['ttc', 'font/collection'],\n    ['ttf', 'font/ttf'],\n    ['ttl', 'text/turtle'],\n    ['ttml', 'application/ttml+xml'],\n    ['twd', 'application/vnd.simtech-mindmapper'],\n    ['twds', 'application/vnd.simtech-mindmapper'],\n    ['txd', 'application/vnd.genomatix.tuxedo'],\n    ['txf', 'application/vnd.mobius.txf'],\n    ['txt', 'text/plain'],\n    ['u8dsn', 'message/global-delivery-status'],\n    ['u8hdr', 'message/global-headers'],\n    ['u8mdn', 'message/global-disposition-notification'],\n    ['u8msg', 'message/global'],\n    ['u32', 'application/x-authorware-bin'],\n    ['ubj', 'application/ubjson'],\n    ['udeb', 'application/x-debian-package'],\n    ['ufd', 'application/vnd.ufdl'],\n    ['ufdl', 'application/vnd.ufdl'],\n    ['ulx', 'application/x-glulx'],\n    ['umj', 'application/vnd.umajin'],\n    ['unityweb', 'application/vnd.unity'],\n    ['uoml', 'application/vnd.uoml+xml'],\n    ['uri', 'text/uri-list'],\n    ['uris', 'text/uri-list'],\n    ['urls', 'text/uri-list'],\n    ['usdz', 'model/vnd.usdz+zip'],\n    ['ustar', 'application/x-ustar'],\n    ['utz', 'application/vnd.uiq.theme'],\n    ['uu', 'text/x-uuencode'],\n    ['uva', 'audio/vnd.dece.audio'],\n    ['uvd', 'application/vnd.dece.data'],\n    ['uvf', 'application/vnd.dece.data'],\n    ['uvg', 'image/vnd.dece.graphic'],\n    ['uvh', 'video/vnd.dece.hd'],\n    ['uvi', 'image/vnd.dece.graphic'],\n    ['uvm', 'video/vnd.dece.mobile'],\n    ['uvp', 'video/vnd.dece.pd'],\n    ['uvs', 'video/vnd.dece.sd'],\n    ['uvt', 'application/vnd.dece.ttml+xml'],\n    ['uvu', 'video/vnd.uvvu.mp4'],\n    ['uvv', 'video/vnd.dece.video'],\n    ['uvva', 'audio/vnd.dece.audio'],\n    ['uvvd', 'application/vnd.dece.data'],\n    ['uvvf', 'application/vnd.dece.data'],\n    ['uvvg', 'image/vnd.dece.graphic'],\n    ['uvvh', 'video/vnd.dece.hd'],\n    ['uvvi', 'image/vnd.dece.graphic'],\n    ['uvvm', 'video/vnd.dece.mobile'],\n    ['uvvp', 'video/vnd.dece.pd'],\n    ['uvvs', 'video/vnd.dece.sd'],\n    ['uvvt', 'application/vnd.dece.ttml+xml'],\n    ['uvvu', 'video/vnd.uvvu.mp4'],\n    ['uvvv', 'video/vnd.dece.video'],\n    ['uvvx', 'application/vnd.dece.unspecified'],\n    ['uvvz', 'application/vnd.dece.zip'],\n    ['uvx', 'application/vnd.dece.unspecified'],\n    ['uvz', 'application/vnd.dece.zip'],\n    ['vbox', 'application/x-virtualbox-vbox'],\n    ['vbox-extpack', 'application/x-virtualbox-vbox-extpack'],\n    ['vcard', 'text/vcard'],\n    ['vcd', 'application/x-cdlink'],\n    ['vcf', 'text/x-vcard'],\n    ['vcg', 'application/vnd.groove-vcard'],\n    ['vcs', 'text/x-vcalendar'],\n    ['vcx', 'application/vnd.vcx'],\n    ['vdi', 'application/x-virtualbox-vdi'],\n    ['vds', 'model/vnd.sap.vds'],\n    ['vhd', 'application/x-virtualbox-vhd'],\n    ['vis', 'application/vnd.visionary'],\n    ['viv', 'video/vnd.vivo'],\n    ['vlc', 'application/videolan'],\n    ['vmdk', 'application/x-virtualbox-vmdk'],\n    ['vob', 'video/x-ms-vob'],\n    ['vor', 'application/vnd.stardivision.writer'],\n    ['vox', 'application/x-authorware-bin'],\n    ['vrml', 'model/vrml'],\n    ['vsd', 'application/vnd.visio'],\n    ['vsf', 'application/vnd.vsf'],\n    ['vss', 'application/vnd.visio'],\n    ['vst', 'application/vnd.visio'],\n    ['vsw', 'application/vnd.visio'],\n    ['vtf', 'image/vnd.valve.source.texture'],\n    ['vtt', 'text/vtt'],\n    ['vtu', 'model/vnd.vtu'],\n    ['vxml', 'application/voicexml+xml'],\n    ['w3d', 'application/x-director'],\n    ['wad', 'application/x-doom'],\n    ['wadl', 'application/vnd.sun.wadl+xml'],\n    ['war', 'application/java-archive'],\n    ['wasm', 'application/wasm'],\n    ['wav', 'audio/x-wav'],\n    ['wax', 'audio/x-ms-wax'],\n    ['wbmp', 'image/vnd.wap.wbmp'],\n    ['wbs', 'application/vnd.criticaltools.wbs+xml'],\n    ['wbxml', 'application/wbxml'],\n    ['wcm', 'application/vnd.ms-works'],\n    ['wdb', 'application/vnd.ms-works'],\n    ['wdp', 'image/vnd.ms-photo'],\n    ['weba', 'audio/webm'],\n    ['webapp', 'application/x-web-app-manifest+json'],\n    ['webm', 'video/webm'],\n    ['webmanifest', 'application/manifest+json'],\n    ['webp', 'image/webp'],\n    ['wg', 'application/vnd.pmi.widget'],\n    ['wgt', 'application/widget'],\n    ['wks', 'application/vnd.ms-works'],\n    ['wm', 'video/x-ms-wm'],\n    ['wma', 'audio/x-ms-wma'],\n    ['wmd', 'application/x-ms-wmd'],\n    ['wmf', 'image/wmf'],\n    ['wml', 'text/vnd.wap.wml'],\n    ['wmlc', 'application/wmlc'],\n    ['wmls', 'text/vnd.wap.wmlscript'],\n    ['wmlsc', 'application/vnd.wap.wmlscriptc'],\n    ['wmv', 'video/x-ms-wmv'],\n    ['wmx', 'video/x-ms-wmx'],\n    ['wmz', 'application/x-msmetafile'],\n    ['woff', 'font/woff'],\n    ['woff2', 'font/woff2'],\n    ['word', 'application/msword'],\n    ['wpd', 'application/vnd.wordperfect'],\n    ['wpl', 'application/vnd.ms-wpl'],\n    ['wps', 'application/vnd.ms-works'],\n    ['wqd', 'application/vnd.wqd'],\n    ['wri', 'application/x-mswrite'],\n    ['wrl', 'model/vrml'],\n    ['wsc', 'message/vnd.wfa.wsc'],\n    ['wsdl', 'application/wsdl+xml'],\n    ['wspolicy', 'application/wspolicy+xml'],\n    ['wtb', 'application/vnd.webturbo'],\n    ['wvx', 'video/x-ms-wvx'],\n    ['x3d', 'model/x3d+xml'],\n    ['x3db', 'model/x3d+fastinfoset'],\n    ['x3dbz', 'model/x3d+binary'],\n    ['x3dv', 'model/x3d-vrml'],\n    ['x3dvz', 'model/x3d+vrml'],\n    ['x3dz', 'model/x3d+xml'],\n    ['x32', 'application/x-authorware-bin'],\n    ['x_b', 'model/vnd.parasolid.transmit.binary'],\n    ['x_t', 'model/vnd.parasolid.transmit.text'],\n    ['xaml', 'application/xaml+xml'],\n    ['xap', 'application/x-silverlight-app'],\n    ['xar', 'application/vnd.xara'],\n    ['xav', 'application/xcap-att+xml'],\n    ['xbap', 'application/x-ms-xbap'],\n    ['xbd', 'application/vnd.fujixerox.docuworks.binder'],\n    ['xbm', 'image/x-xbitmap'],\n    ['xca', 'application/xcap-caps+xml'],\n    ['xcs', 'application/calendar+xml'],\n    ['xdf', 'application/xcap-diff+xml'],\n    ['xdm', 'application/vnd.syncml.dm+xml'],\n    ['xdp', 'application/vnd.adobe.xdp+xml'],\n    ['xdssc', 'application/dssc+xml'],\n    ['xdw', 'application/vnd.fujixerox.docuworks'],\n    ['xel', 'application/xcap-el+xml'],\n    ['xenc', 'application/xenc+xml'],\n    ['xer', 'application/patch-ops-error+xml'],\n    ['xfdf', 'application/vnd.adobe.xfdf'],\n    ['xfdl', 'application/vnd.xfdl'],\n    ['xht', 'application/xhtml+xml'],\n    ['xhtml', 'application/xhtml+xml'],\n    ['xhvml', 'application/xv+xml'],\n    ['xif', 'image/vnd.xiff'],\n    ['xl', 'application/excel'],\n    ['xla', 'application/vnd.ms-excel'],\n    ['xlam', 'application/vnd.ms-excel.addin.macroEnabled.12'],\n    ['xlc', 'application/vnd.ms-excel'],\n    ['xlf', 'application/xliff+xml'],\n    ['xlm', 'application/vnd.ms-excel'],\n    ['xls', 'application/vnd.ms-excel'],\n    ['xlsb', 'application/vnd.ms-excel.sheet.binary.macroEnabled.12'],\n    ['xlsm', 'application/vnd.ms-excel.sheet.macroEnabled.12'],\n    ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],\n    ['xlt', 'application/vnd.ms-excel'],\n    ['xltm', 'application/vnd.ms-excel.template.macroEnabled.12'],\n    ['xltx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.template'],\n    ['xlw', 'application/vnd.ms-excel'],\n    ['xm', 'audio/xm'],\n    ['xml', 'application/xml'],\n    ['xns', 'application/xcap-ns+xml'],\n    ['xo', 'application/vnd.olpc-sugar'],\n    ['xop', 'application/xop+xml'],\n    ['xpi', 'application/x-xpinstall'],\n    ['xpl', 'application/xproc+xml'],\n    ['xpm', 'image/x-xpixmap'],\n    ['xpr', 'application/vnd.is-xpr'],\n    ['xps', 'application/vnd.ms-xpsdocument'],\n    ['xpw', 'application/vnd.intercon.formnet'],\n    ['xpx', 'application/vnd.intercon.formnet'],\n    ['xsd', 'application/xml'],\n    ['xsl', 'application/xml'],\n    ['xslt', 'application/xslt+xml'],\n    ['xsm', 'application/vnd.syncml+xml'],\n    ['xspf', 'application/xspf+xml'],\n    ['xul', 'application/vnd.mozilla.xul+xml'],\n    ['xvm', 'application/xv+xml'],\n    ['xvml', 'application/xv+xml'],\n    ['xwd', 'image/x-xwindowdump'],\n    ['xyz', 'chemical/x-xyz'],\n    ['xz', 'application/x-xz'],\n    ['yaml', 'text/yaml'],\n    ['yang', 'application/yang'],\n    ['yin', 'application/yin+xml'],\n    ['yml', 'text/yaml'],\n    ['ymp', 'text/x-suse-ymp'],\n    ['z', 'application/x-compress'],\n    ['z1', 'application/x-zmachine'],\n    ['z2', 'application/x-zmachine'],\n    ['z3', 'application/x-zmachine'],\n    ['z4', 'application/x-zmachine'],\n    ['z5', 'application/x-zmachine'],\n    ['z6', 'application/x-zmachine'],\n    ['z7', 'application/x-zmachine'],\n    ['z8', 'application/x-zmachine'],\n    ['zaz', 'application/vnd.zzazz.deck+xml'],\n    ['zip', 'application/zip'],\n    ['zir', 'application/vnd.zul'],\n    ['zirz', 'application/vnd.zul'],\n    ['zmm', 'application/vnd.handheld-entertainment+xml'],\n    ['zsh', 'text/x-scriptzsh']\n]);\n\n\nexport function toFileWithPath(file: FileWithPath, path?: string, h?: FileSystemHandle): FileWithPath {\n    const f = withMimeType(file);\n    const {webkitRelativePath} = file;\n    const p = typeof path === 'string'\n        ? path\n        // If <input webkitdirectory> is set,\n        // the File will have a {webkitRelativePath} property\n        // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n        : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0\n            ? webkitRelativePath\n            : `./${file.name}`;\n    if (typeof f.path !== 'string') { // on electron, path is already set to the absolute path\n        setObjProp(f, 'path', p);\n    }\n    if (h !== undefined) {\n        Object.defineProperty(f, 'handle', {\n            value: h,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n    // Always populate a relative path so that even electron apps have access to a relativePath value\n    setObjProp(f, 'relativePath', p);\n    return f;\n}\n\nexport interface FileWithPath extends File {\n    readonly path?: string;\n    readonly handle?: FileSystemFileHandle;\n    readonly relativePath?: string;\n}\n\nfunction withMimeType(file: FileWithPath) {\n    const {name} = file;\n    const hasExtension = name && name.lastIndexOf('.') !== -1;\n\n    if (hasExtension && !file.type) {\n        const ext = name.split('.')\n            .pop()!.toLowerCase();\n        const type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, 'type', {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n\n    return file;\n}\n\nfunction setObjProp(f: FileWithPath, key: string, value: string) {\n    Object.defineProperty(f, key, {\n        value,\n        writable: false,\n        configurable: false,\n        enumerable: true\n    })\n}\n"], "mappings": "AAAA,OAAO,MAAMA,iBAAiB,GAAG,IAAIC,GAAG,CAAC;AACrC;AACA,CAAC,KAAK,EAAE,8CAA8C,CAAC,EACvD,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC9B,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,IAAI,EAAE,6BAA6B,CAAC,EACrC,CAAC,MAAM,EAAE,6BAA6B,CAAC,EACvC,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,IAAI,EAAE,qCAAqC,CAAC,EAC7C,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,sCAAsC,CAAC,EAC/C,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,OAAO,EAAE,yBAAyB,CAAC,EACpC,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,OAAO,EAAE,6BAA6B,CAAC,EACxC,CAAC,IAAI,EAAE,iBAAiB,CAAC,EACzB,CAAC,KAAK,EAAE,cAAc,CAAC,EACvB,CAAC,MAAM,EAAE,cAAc,CAAC,EACxB,CAAC,MAAM,EAAE,cAAc,CAAC,EACxB,CAAC,KAAK,EAAE,6DAA6D,CAAC,EACtE,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,yCAAyC,CAAC,EAClD,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,UAAU,EAAE,qBAAqB,CAAC,EACnC,CAAC,aAAa,EAAE,8BAA8B,CAAC,EAC/C,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,SAAS,EAAE,yBAAyB,CAAC,EACtC,CAAC,aAAa,EAAE,6BAA6B,CAAC,EAC9C,CAAC,SAAS,EAAE,yBAAyB,CAAC,EACtC,CAAC,KAAK,EAAE,sCAAsC,CAAC,EAC/C,CAAC,IAAI,EAAE,YAAY,CAAC,EACpB,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,CAAC,KAAK,EAAE,uCAAuC,CAAC,EAChD,CAAC,KAAK,EAAE,uCAAuC,CAAC,EAChD,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,OAAO,EAAE,qBAAqB,CAAC,EAChC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC9B,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,OAAO,EAAE,qBAAqB,CAAC,EAChC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,MAAM,EAAE,mCAAmC,CAAC,EAC7C,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,4BAA4B,CAAC,EACtC,CAAC,KAAK,EAAE,oCAAoC,CAAC,EAC7C,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,0BAA0B,CAAC,EACpC,CAAC,KAAK,EAAE,qCAAqC,CAAC,EAC9C,CAAC,MAAM,EAAE,gBAAgB,CAAC,EAC1B,CAAC,QAAQ,EAAE,0BAA0B,CAAC,EACtC,CAAC,IAAI,EAAE,oBAAoB,CAAC,EAC5B,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,GAAG,EAAE,UAAU,CAAC,EACjB,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,QAAQ,EAAE,8CAA8C,CAAC,EAC1D,CAAC,QAAQ,EAAE,kDAAkD,CAAC,EAC9D,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,IAAI,EAAE,UAAU,CAAC,EAClB,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,OAAO,EAAE,uBAAuB,CAAC,EAClC,CAAC,SAAS,EAAE,8BAA8B,CAAC,EAC3C,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,OAAO,EAAE,oCAAoC,CAAC,EAC/C,CAAC,OAAO,EAAE,6BAA6B,CAAC,EACxC,CAAC,OAAO,EAAE,4BAA4B,CAAC,EACvC,CAAC,OAAO,EAAE,yBAAyB,CAAC,EACpC,CAAC,OAAO,EAAE,yBAAyB,CAAC,EACpC,CAAC,OAAO,EAAE,wBAAwB,CAAC,EACnC,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,OAAO,EAAE,8BAA8B,CAAC,EACzC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC9B,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,MAAM,EAAE,4BAA4B,CAAC,EACtC,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,KAAK,EAAE,wDAAwD,CAAC,EACjE,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,OAAO,EAAE,0BAA0B,CAAC,EACrC,CAAC,MAAM,EAAE,wCAAwC,CAAC,EAClD,CAAC,MAAM,EAAE,uCAAuC,CAAC,EACjD,CAAC,MAAM,EAAE,wCAAwC,CAAC,EAClD,CAAC,MAAM,EAAE,wCAAwC,CAAC,EAClD,CAAC,MAAM,EAAE,+BAA+B,CAAC,EACzC,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,MAAM,EAAE,iBAAiB,CAAC,EAC3B,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,KAAK,EAAE,yCAAyC,CAAC,EAClD,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,QAAQ,EAAE,mBAAmB,CAAC,EAC/B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC9B,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,YAAY,EAAE,gCAAgC,CAAC,EAChD,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,0CAA0C,CAAC,EACnD,CAAC,MAAM,EAAE,iBAAiB,CAAC,EAC3B,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,IAAI,EAAE,sBAAsB,CAAC,EAC9B,CAAC,MAAM,EAAE,eAAe,CAAC,EACzB,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,UAAU,EAAE,2BAA2B,CAAC,EACzC,CAAC,UAAU,EAAE,0BAA0B,CAAC,EACxC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,OAAO,EAAE,qBAAqB,CAAC,EAChC,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,QAAQ,EAAE,0BAA0B,CAAC,EACtC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,MAAM,EAAE,8BAA8B,CAAC,EACxC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,0BAA0B,EAAE,kCAAkC,CAAC,EAChE,CAAC,MAAM,EAAE,0BAA0B,CAAC,EACpC,CAAC,OAAO,EAAE,0BAA0B,CAAC,EACrC,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,MAAM,EAAE,gBAAgB,CAAC,EAC1B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,MAAM,EAAE,kDAAkD,CAAC,EAC5D,CAAC,MAAM,EAAE,yEAAyE,CAAC,EACnF,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,MAAM,EAAE,kDAAkD,CAAC,EAC5D,CAAC,MAAM,EAAE,yEAAyE,CAAC,EACnF,CAAC,IAAI,EAAE,yBAAyB,CAAC,EACjC,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,MAAM,EAAE,iBAAiB,CAAC,EAC3B,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,OAAO,EAAE,kBAAkB,CAAC,EAC7B,CAAC,MAAM,EAAE,0BAA0B,CAAC,EACpC,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,WAAW,EAAE,2BAA2B,CAAC,EAC1C,CAAC,WAAW,EAAE,2BAA2B,CAAC,EAC1C,CAAC,WAAW,EAAE,2BAA2B,CAAC,EAC1C,CAAC,MAAM,EAAE,wBAAwB,CAAC,EAClC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,MAAM,EAAE,wBAAwB,CAAC,EAClC,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,WAAW,EAAE,2BAA2B,CAAC,EAC1C,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,IAAI,EAAE,0BAA0B,CAAC,EAClC,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,GAAG,EAAE,gBAAgB,CAAC,EACvB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,MAAM,EAAE,yCAAyC,CAAC,EACnD,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,WAAW,EAAE,wCAAwC,CAAC,EACvD,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,IAAI,EAAE,kBAAkB,CAAC,EAC1B,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,MAAM,EAAE,cAAc,CAAC,EACxB,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,cAAc,CAAC,EACvB,CAAC,IAAI,EAAE,4BAA4B,CAAC,EACpC,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,IAAI,EAAE,6CAA6C,CAAC,EACrD,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,OAAO,EAAE,4BAA4B,CAAC,EACvC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,qDAAqD,CAAC,EAC9D,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,MAAM,EAAE,2BAA2B,CAAC,EACrC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,IAAI,EAAE,aAAa,CAAC,EACrB,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,MAAM,EAAE,sCAAsC,CAAC,EAChD,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,SAAS,EAAE,sBAAsB,CAAC,EACnC,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,yCAAyC,CAAC,EAClD,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,MAAM,EAAE,iBAAiB,CAAC,EAC3B,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,UAAU,EAAE,wBAAwB,CAAC,EACtC,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,QAAQ,EAAE,0BAA0B,CAAC,EACtC,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,OAAO,EAAE,sBAAsB,CAAC,EACjC,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,QAAQ,EAAE,yCAAyC,CAAC,EACrD,CAAC,SAAS,EAAE,0CAA0C,CAAC,EACvD,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC9B,CAAC,KAAK,EAAE,qCAAqC,CAAC,EAC9C,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,IAAI,EAAE,mBAAmB,CAAC,EAC3B,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,IAAI,EAAE,kBAAkB,CAAC,EAC1B,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,GAAG,EAAE,UAAU,CAAC,EACjB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,OAAO,EAAE,qBAAqB,CAAC,EAChC,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,OAAO,EAAE,qBAAqB,CAAC,EAChC,CAAC,MAAM,EAAE,aAAa,CAAC,EACvB,CAAC,MAAM,EAAE,2BAA2B,CAAC,EACrC,CAAC,IAAI,EAAE,UAAU,CAAC,EAClB,CAAC,OAAO,EAAE,mBAAmB,CAAC,EAC9B,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,MAAM,EAAE,yBAAyB,CAAC,EACnC,CAAC,MAAM,EAAE,yBAAyB,CAAC,EACnC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,MAAM,EAAE,4BAA4B,CAAC,EACtC,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,cAAc,CAAC,EACvB,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,yCAAyC,CAAC,EAClD,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,KAAK,EAAE,4CAA4C,CAAC,EACrD,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,IAAI,EAAE,YAAY,CAAC,EACpB,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,OAAO,EAAE,uBAAuB,CAAC,EAClC,CAAC,SAAS,EAAE,oCAAoC,CAAC,EACjD,CAAC,MAAM,EAAE,uCAAuC,CAAC,EACjD,CAAC,OAAO,EAAE,mBAAmB,CAAC,EAC9B,CAAC,KAAK,EAAE,wCAAwC,CAAC,EACjD,CAAC,KAAK,EAAE,uCAAuC,CAAC,EAChD,CAAC,KAAK,EAAE,yCAAyC,CAAC,EAClD,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,6CAA6C,CAAC,EACtD,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,SAAS,EAAE,iCAAiC,CAAC,EAC9C,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC9B,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,MAAM,EAAE,8BAA8B,CAAC,EACxC,CAAC,MAAM,EAAE,oCAAoC,CAAC,EAC9C,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,OAAO,EAAE,mBAAmB,CAAC,EAC9B,CAAC,QAAQ,EAAE,qBAAqB,CAAC;AACjC;AACA,CAAC,OAAO,EAAE,mBAAmB,CAAC,EAC9B,CAAC,QAAQ,EAAE,yBAAyB,CAAC,EACrC,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,QAAQ,EAAE,4BAA4B,CAAC,EACxC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,wBAAwB,CAAC,EAClC,CAAC,KAAK,EAAE,oCAAoC,CAAC,EAC7C,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,sCAAsC,CAAC,EAC/C,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,MAAM,EAAE,6BAA6B,CAAC,EACvC,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,QAAQ,EAAE,6BAA6B,CAAC,EACzC,CAAC,OAAO,EAAE,qBAAqB,CAAC,EAChC,CAAC,KAAK,EAAE,oDAAoD,CAAC,EAC7D,CAAC,KAAK,EAAE,yDAAyD,CAAC,EAClE,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,QAAQ,EAAE,oCAAoC,CAAC,EAChD,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,UAAU,EAAE,4BAA4B,CAAC,EAC1C,CAAC,SAAS,EAAE,4BAA4B,CAAC,EACzC,CAAC,WAAW,EAAE,mBAAmB,CAAC,EAClC,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,SAAS,EAAE,sBAAsB,CAAC,EACnC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,MAAM,EAAE,4BAA4B,CAAC,EACtC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,MAAM,EAAE,+BAA+B,CAAC,EACzC,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,IAAI,EAAE,yBAAyB,CAAC,EACjC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,MAAM,EAAE,yBAAyB,CAAC,EACnC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,OAAO,EAAE,4BAA4B,CAAC,EACvC,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,UAAU,EAAE,qBAAqB,CAAC,EACnC,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,UAAU,EAAE,eAAe,CAAC,EAC7B,CAAC,QAAQ,EAAE,wBAAwB,CAAC,EACpC,CAAC,IAAI,EAAE,yBAAyB,CAAC,EACjC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,OAAO,EAAE,qBAAqB,CAAC,EAChC,CAAC,IAAI,EAAE,eAAe,CAAC,EACvB,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,IAAI,EAAE,YAAY,CAAC,EACpB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,OAAO,EAAE,2BAA2B,CAAC,EACtC,CAAC,UAAU,EAAE,0BAA0B,CAAC,EACxC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,wCAAwC,CAAC,EACjD,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,MAAM,EAAE,gBAAgB,CAAC,EAC1B,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,sCAAsC,CAAC,EAC/C,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,MAAM,EAAE,gCAAgC,CAAC,EAC1C,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,OAAO,EAAE,mBAAmB,CAAC,EAC9B,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,MAAM,EAAE,iBAAiB,CAAC,EAC3B,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,KAAK,EAAE,oCAAoC,CAAC,EAC7C,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,MAAM,EAAE,qCAAqC,CAAC,EAC/C,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,oCAAoC,CAAC,EAC7C,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,MAAM,EAAE,yBAAyB,CAAC,EACnC,CAAC,IAAI,EAAE,YAAY,CAAC,EACpB,CAAC,OAAO,EAAE,oCAAoC,CAAC,EAC/C,CAAC,OAAO,EAAE,4BAA4B,CAAC,EACvC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,6BAA6B,CAAC,EACvC,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,yBAAyB,CAAC,EACnC,CAAC,UAAU,EAAE,wCAAwC,CAAC,EACtD,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,oCAAoC,CAAC,EAC7C,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,oCAAoC,CAAC,EAC7C,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC9B,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,QAAQ,EAAE,8CAA8C,CAAC,EAC1D,CAAC,IAAI,EAAE,SAAS,CAAC,EACjB,CAAC,IAAI,EAAE,yBAAyB,CAAC,EACjC,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,IAAI,EAAE,sBAAsB,CAAC,EAC9B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,OAAO,EAAE,mCAAmC,CAAC,EAC9C,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,oCAAoC,CAAC,EAC7C,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,IAAI,EAAE,qBAAqB,CAAC,EAC7B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,IAAI,EAAE,uBAAuB,CAAC,EAC/B,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,SAAS,EAAE,wCAAwC,CAAC,EACrD,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,MAAM,EAAE,mCAAmC,CAAC,EAC7C,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,6CAA6C,CAAC,EACtD,CAAC,KAAK,EAAE,0CAA0C,CAAC,EACnD,CAAC,KAAK,EAAE,4CAA4C,CAAC,EACrD,CAAC,MAAM,EAAE,qDAAqD,CAAC,EAC/D,CAAC,KAAK,EAAE,6CAA6C,CAAC,EACtD,CAAC,KAAK,EAAE,0CAA0C,CAAC,EACnD,CAAC,KAAK,EAAE,gDAAgD,CAAC,EACzD,CAAC,KAAK,EAAE,iDAAiD,CAAC,EAC1D,CAAC,KAAK,EAAE,gDAAgD,CAAC,EACzD,CAAC,KAAK,EAAE,yCAAyC,CAAC,EAClD,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,mBAAmB,CAAC,EAC7B,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,OAAO,EAAE,uBAAuB,CAAC,EAClC,CAAC,QAAQ,EAAE,qBAAqB,CAAC,EACjC,CAAC,QAAQ,EAAE,qBAAqB,CAAC,EACjC,CAAC,QAAQ,EAAE,qBAAqB,CAAC,EACjC,CAAC,SAAS,EAAE,qBAAqB,CAAC,EAClC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,MAAM,EAAE,aAAa,CAAC,EACvB,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,wCAAwC,CAAC,EACjD,CAAC,QAAQ,EAAE,mDAAmD,CAAC,EAC/D,CAAC,KAAK,EAAE,wCAAwC,CAAC,EACjD,CAAC,KAAK,EAAE,mDAAmD,CAAC,EAC5D,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,KAAK,EAAE,sDAAsD,CAAC,EAC/D,CAAC,KAAK,EAAE,6CAA6C,CAAC,EACtD,CAAC,KAAK,EAAE,mDAAmD,CAAC,EAC5D,CAAC,KAAK,EAAE,0DAA0D,CAAC,EACnE,CAAC,KAAK,EAAE,yDAAyD,CAAC,EAClE,CAAC,KAAK,EAAE,kDAAkD,CAAC,EAC3D,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,KAAK,EAAE,yCAAyC,CAAC,EAClD,CAAC,GAAG,EAAE,eAAe,CAAC,EACtB,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,IAAI,EAAE,mBAAmB,CAAC,EAC3B,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,OAAO,EAAE,oCAAoC,CAAC,EAC/C,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,MAAM,EAAE,8BAA8B,CAAC,EACxC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,OAAO,EAAE,0BAA0B,CAAC,EACrC,CAAC,KAAK,EAAE,cAAc,CAAC,EACvB,CAAC,OAAO,EAAE,4BAA4B,CAAC,EACvC,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,MAAM,EAAE,yBAAyB,CAAC,EACnC,CAAC,MAAM,EAAE,yBAAyB,CAAC,EACnC,CAAC,MAAM,EAAE,gCAAgC,CAAC,EAC1C,CAAC,OAAO,EAAE,yBAAyB,CAAC,EACpC,CAAC,KAAK,EAAE,cAAc,CAAC,EACvB,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,SAAS,EAAE,0BAA0B,CAAC,EACvC,CAAC,QAAQ,EAAE,8BAA8B,CAAC,EAC1C,CAAC,IAAI,EAAE,oBAAoB,CAAC,EAC5B,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,IAAI,EAAE,oBAAoB,CAAC,EAC5B,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,SAAS,EAAE,kCAAkC,CAAC,EAC/C,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,MAAM,EAAE,4DAA4D,CAAC,EACtE,CAAC,MAAM,EAAE,uEAAuE,CAAC,EACjF,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,MAAM,EAAE,qDAAqD,CAAC,EAC/D,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,MAAM,EAAE,yDAAyD,CAAC,EACnE,CAAC,MAAM,EAAE,wEAAwE,CAAC,EAClF,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,MAAM,EAAE,4DAA4D,CAAC,EACtE,CAAC,MAAM,EAAE,2EAA2E,CAAC,EACrF,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,OAAO,EAAE,4BAA4B,CAAC,EACvC,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,SAAS,EAAE,sBAAsB,CAAC,EACnC,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,MAAM,EAAE,2BAA2B,CAAC,EACrC,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,uCAAuC,CAAC,EAChD,CAAC,IAAI,EAAE,iBAAiB,CAAC,EACzB,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,IAAI,EAAE,mBAAmB,CAAC,EAC3B,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,MAAM,EAAE,uBAAuB,CAAC,EACjC,CAAC,MAAM,EAAE,2BAA2B,CAAC,EACrC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,WAAW,EAAE,uCAAuC,CAAC,EACtD,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,MAAM,EAAE,6BAA6B,CAAC,EACvC,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,qCAAqC,CAAC,EAC9C,CAAC,IAAI,EAAE,gCAAgC,CAAC,EACxC,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,KAAK,EAAE,qCAAqC,CAAC,EAC9C,CAAC,IAAI,EAAE,sBAAsB,CAAC,EAC9B,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,uCAAuC,CAAC,EAChD,CAAC,MAAM,EAAE,kCAAkC,CAAC,EAC5C,CAAC,KAAK,EAAE,qCAAqC,CAAC,EAC9C,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,MAAM,EAAE,qCAAqC,CAAC,EAC/C,CAAC,MAAM,EAAE,oCAAoC,CAAC,EAC9C,CAAC,IAAI,EAAE,0BAA0B,CAAC,EAClC,CAAC,IAAI,EAAE,8BAA8B,CAAC,EACtC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,MAAM,EAAE,2BAA2B,CAAC,EACrC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,QAAQ,EAAE,8BAA8B,CAAC,EAC1C,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,MAAM,EAAE,2BAA2B,CAAC,EACrC,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,CAAC,GAAG,EAAE,YAAY,CAAC,EACnB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,MAAM,EAAE,aAAa,CAAC,EACvB,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,IAAI,EAAE,sCAAsC,CAAC,EAC9C,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,MAAM,EAAE,aAAa,CAAC,EACvB,CAAC,OAAO,EAAE,qBAAqB,CAAC,EAChC,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,sCAAsC,CAAC,EAC/C,CAAC,MAAM,EAAE,iCAAiC,CAAC,EAC3C,CAAC,MAAM,EAAE,iCAAiC,CAAC,EAC3C,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,qCAAqC,CAAC,EAC9C,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,MAAM,EAAE,2BAA2B,CAAC,EACrC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,QAAQ,EAAE,uBAAuB,CAAC,EACnC,CAAC,SAAS,EAAE,wBAAwB,CAAC,EACrC,CAAC,KAAK,EAAE,oCAAoC,CAAC,EAC7C,CAAC,QAAQ,EAAE,oCAAoC,CAAC,EAChD,CAAC,QAAQ,EAAE,yCAAyC,CAAC,EACrD,CAAC,WAAW,EAAE,sCAAsC,CAAC,EACrD,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,4CAA4C,CAAC,EACrD,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,IAAI,EAAE,kBAAkB,CAAC,EAC1B,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC9B,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,OAAO,EAAE,WAAW,CAAC,EACtB,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,OAAO,EAAE,mBAAmB,CAAC,EAC9B,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,MAAM,EAAE,iCAAiC,CAAC,EAC3C,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,MAAM,EAAE,wBAAwB,CAAC,EAClC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,MAAM,EAAE,qDAAqD,CAAC,EAC/D,CAAC,MAAM,EAAE,oEAAoE,CAAC,EAC9E,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,IAAI,EAAE,qCAAqC,CAAC,EAC7C,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,OAAO,EAAE,mCAAmC,CAAC,EAC9C,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,IAAI,EAAE,0BAA0B,CAAC,EAClC,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,KAAK,EAAE,oCAAoC,CAAC,EAC7C,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC9B,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,IAAI,EAAE,sCAAsC,CAAC,EAC9C,CAAC,KAAK,EAAE,uCAAuC,CAAC,EAChD,CAAC,KAAK,EAAE,uCAAuC,CAAC,EAChD,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,0CAA0C,CAAC,EACnD,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,MAAM,EAAE,gBAAgB,CAAC,EAC1B,CAAC,OAAO,EAAE,oBAAoB,CAAC,EAC/B,CAAC,MAAM,EAAE,gBAAgB,CAAC,EAC1B,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,yCAAyC,CAAC,EAClD,CAAC,MAAM,EAAE,aAAa,CAAC,EACvB,CAAC,QAAQ,EAAE,aAAa,CAAC,EACzB,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,MAAM,EAAE,8BAA8B,CAAC,EACxC,CAAC,SAAS,EAAE,uBAAuB,CAAC,EACpC,CAAC,QAAQ,EAAE,sBAAsB,CAAC,EAClC,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,MAAM,EAAE,eAAe,CAAC,EACzB,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,oCAAoC,CAAC,EAC7C,CAAC,SAAS,EAAE,sBAAsB,CAAC,EACnC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,uCAAuC,CAAC,EAChD,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,GAAG,EAAE,YAAY,CAAC,EACnB,CAAC,IAAI,EAAE,0BAA0B,CAAC,EAClC,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,QAAQ,EAAE,uBAAuB,CAAC,EACnC,CAAC,KAAK,EAAE,2CAA2C,CAAC,EACpD,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,MAAM,EAAE,4BAA4B,CAAC,EACtC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,IAAI,EAAE,gCAAgC,CAAC,EACxC,CAAC,SAAS,EAAE,+BAA+B,CAAC,EAC5C,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,WAAW,EAAE,qBAAqB,CAAC,EACpC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,MAAM,EAAE,uBAAuB,CAAC,EACjC,CAAC,SAAS,EAAE,uBAAuB,CAAC,EACpC,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,MAAM,EAAE,gCAAgC,CAAC,EAC1C,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,IAAI,EAAE,mBAAmB,CAAC,EAC3B,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,SAAS,EAAE,0BAA0B,CAAC,EACvC,CAAC,KAAK,EAAE,sCAAsC,CAAC,EAC/C,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,IAAI,EAAE,YAAY,CAAC,EACpB,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,IAAI,EAAE,YAAY,CAAC,EACpB,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,oCAAoC,CAAC,EAC7C,CAAC,MAAM,EAAE,oCAAoC,CAAC,EAC9C,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,OAAO,EAAE,gCAAgC,CAAC,EAC3C,CAAC,OAAO,EAAE,wBAAwB,CAAC,EACnC,CAAC,OAAO,EAAE,yCAAyC,CAAC,EACpD,CAAC,OAAO,EAAE,gBAAgB,CAAC,EAC3B,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,MAAM,EAAE,8BAA8B,CAAC,EACxC,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,UAAU,EAAE,uBAAuB,CAAC,EACrC,CAAC,MAAM,EAAE,0BAA0B,CAAC,EACpC,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,MAAM,EAAE,eAAe,CAAC,EACzB,CAAC,MAAM,EAAE,eAAe,CAAC,EACzB,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC9B,CAAC,OAAO,EAAE,qBAAqB,CAAC,EAChC,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,IAAI,EAAE,iBAAiB,CAAC,EACzB,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,MAAM,EAAE,2BAA2B,CAAC,EACrC,CAAC,MAAM,EAAE,2BAA2B,CAAC,EACrC,CAAC,MAAM,EAAE,wBAAwB,CAAC,EAClC,CAAC,MAAM,EAAE,mBAAmB,CAAC,EAC7B,CAAC,MAAM,EAAE,wBAAwB,CAAC,EAClC,CAAC,MAAM,EAAE,uBAAuB,CAAC,EACjC,CAAC,MAAM,EAAE,mBAAmB,CAAC,EAC7B,CAAC,MAAM,EAAE,mBAAmB,CAAC,EAC7B,CAAC,MAAM,EAAE,+BAA+B,CAAC,EACzC,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC9B,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,MAAM,EAAE,kCAAkC,CAAC,EAC5C,CAAC,MAAM,EAAE,0BAA0B,CAAC,EACpC,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,+BAA+B,CAAC,EACzC,CAAC,cAAc,EAAE,uCAAuC,CAAC,EACzD,CAAC,OAAO,EAAE,YAAY,CAAC,EACvB,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,cAAc,CAAC,EACvB,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAC5B,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,MAAM,EAAE,+BAA+B,CAAC,EACzC,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,KAAK,EAAE,qCAAqC,CAAC,EAC9C,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,KAAK,EAAE,UAAU,CAAC,EACnB,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,MAAM,EAAE,0BAA0B,CAAC,EACpC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,MAAM,EAAE,8BAA8B,CAAC,EACxC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,KAAK,EAAE,aAAa,CAAC,EACtB,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC9B,CAAC,KAAK,EAAE,uCAAuC,CAAC,EAChD,CAAC,OAAO,EAAE,mBAAmB,CAAC,EAC9B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,QAAQ,EAAE,qCAAqC,CAAC,EACjD,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,aAAa,EAAE,2BAA2B,CAAC,EAC5C,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,IAAI,EAAE,4BAA4B,CAAC,EACpC,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,IAAI,EAAE,eAAe,CAAC,EACvB,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,kBAAkB,CAAC,EAC3B,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,MAAM,EAAE,wBAAwB,CAAC,EAClC,CAAC,OAAO,EAAE,gCAAgC,CAAC,EAC3C,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,OAAO,EAAE,YAAY,CAAC,EACvB,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC9B,CAAC,KAAK,EAAE,6BAA6B,CAAC,EACtC,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,YAAY,CAAC,EACrB,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,UAAU,EAAE,0BAA0B,CAAC,EACxC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,KAAK,EAAE,eAAe,CAAC,EACxB,CAAC,MAAM,EAAE,uBAAuB,CAAC,EACjC,CAAC,OAAO,EAAE,kBAAkB,CAAC,EAC7B,CAAC,MAAM,EAAE,gBAAgB,CAAC,EAC1B,CAAC,OAAO,EAAE,gBAAgB,CAAC,EAC3B,CAAC,MAAM,EAAE,eAAe,CAAC,EACzB,CAAC,KAAK,EAAE,8BAA8B,CAAC,EACvC,CAAC,KAAK,EAAE,qCAAqC,CAAC,EAC9C,CAAC,KAAK,EAAE,mCAAmC,CAAC,EAC5C,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,sBAAsB,CAAC,EAC/B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,uBAAuB,CAAC,EACjC,CAAC,KAAK,EAAE,4CAA4C,CAAC,EACrD,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,2BAA2B,CAAC,EACpC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,KAAK,EAAE,+BAA+B,CAAC,EACxC,CAAC,OAAO,EAAE,sBAAsB,CAAC,EACjC,CAAC,KAAK,EAAE,qCAAqC,CAAC,EAC9C,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,MAAM,EAAE,4BAA4B,CAAC,EACtC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,OAAO,EAAE,uBAAuB,CAAC,EAClC,CAAC,OAAO,EAAE,oBAAoB,CAAC,EAC/B,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,IAAI,EAAE,mBAAmB,CAAC,EAC3B,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,gDAAgD,CAAC,EAC1D,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,uDAAuD,CAAC,EACjE,CAAC,MAAM,EAAE,gDAAgD,CAAC,EAC1D,CAAC,MAAM,EAAE,mEAAmE,CAAC,EAC7E,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,MAAM,EAAE,mDAAmD,CAAC,EAC7D,CAAC,MAAM,EAAE,sEAAsE,CAAC,EAChF,CAAC,KAAK,EAAE,0BAA0B,CAAC,EACnC,CAAC,IAAI,EAAE,UAAU,CAAC,EAClB,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,IAAI,EAAE,4BAA4B,CAAC,EACpC,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,yBAAyB,CAAC,EAClC,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAChC,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,wBAAwB,CAAC,EACjC,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,kCAAkC,CAAC,EAC3C,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,4BAA4B,CAAC,EACrC,CAAC,MAAM,EAAE,sBAAsB,CAAC,EAChC,CAAC,KAAK,EAAE,iCAAiC,CAAC,EAC1C,CAAC,KAAK,EAAE,oBAAoB,CAAC,EAC7B,CAAC,MAAM,EAAE,oBAAoB,CAAC,EAC9B,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,gBAAgB,CAAC,EACzB,CAAC,IAAI,EAAE,kBAAkB,CAAC,EAC1B,CAAC,MAAM,EAAE,WAAW,CAAC,EACrB,CAAC,MAAM,EAAE,kBAAkB,CAAC,EAC5B,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,KAAK,EAAE,WAAW,CAAC,EACpB,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,GAAG,EAAE,wBAAwB,CAAC,EAC/B,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,CAAC,IAAI,EAAE,wBAAwB,CAAC,EAChC,CAAC,KAAK,EAAE,gCAAgC,CAAC,EACzC,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAC1B,CAAC,KAAK,EAAE,qBAAqB,CAAC,EAC9B,CAAC,MAAM,EAAE,qBAAqB,CAAC,EAC/B,CAAC,KAAK,EAAE,4CAA4C,CAAC,EACrD,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAC9B,CAAC;AAGF,OAAM,SAAUC,cAAcA,CAACC,IAAkB,EAAEC,IAAa,EAAEC,CAAoB;EAClF,MAAMC,CAAC,GAAGC,YAAY,CAACJ,IAAI,CAAC;EAC5B,MAAM;IAACK;EAAkB,CAAC,GAAGL,IAAI;EACjC,MAAMM,CAAC,GAAG,OAAOL,IAAI,KAAK,QAAQ,GAC5BA;EACF;EACA;EACA;EAAA,EACE,OAAOI,kBAAkB,KAAK,QAAQ,IAAIA,kBAAkB,CAACE,MAAM,GAAG,CAAC,GACnEF,kBAAkB,GAClB,KAAKL,IAAI,CAACQ,IAAI,EAAE;EAC1B,IAAI,OAAOL,CAAC,CAACF,IAAI,KAAK,QAAQ,EAAE;IAAE;IAC9BQ,UAAU,CAACN,CAAC,EAAE,MAAM,EAAEG,CAAC,CAAC;EAC5B;EACA,IAAIJ,CAAC,KAAKQ,SAAS,EAAE;IACjBC,MAAM,CAACC,cAAc,CAACT,CAAC,EAAE,QAAQ,EAAE;MAC/BU,KAAK,EAAEX,CAAC;MACRY,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;KACf,CAAC;EACN;EACA;EACAP,UAAU,CAACN,CAAC,EAAE,cAAc,EAAEG,CAAC,CAAC;EAChC,OAAOH,CAAC;AACZ;AAQA,SAASC,YAAYA,CAACJ,IAAkB;EACpC,MAAM;IAACQ;EAAI,CAAC,GAAGR,IAAI;EACnB,MAAMiB,YAAY,GAAGT,IAAI,IAAIA,IAAI,CAACU,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;EAEzD,IAAID,YAAY,IAAI,CAACjB,IAAI,CAACmB,IAAI,EAAE;IAC5B,MAAMC,GAAG,GAAGZ,IAAI,CAACa,KAAK,CAAC,GAAG,CAAC,CACtBC,GAAG,EAAG,CAACC,WAAW,EAAE;IACzB,MAAMJ,IAAI,GAAGtB,iBAAiB,CAAC2B,GAAG,CAACJ,GAAG,CAAC;IACvC,IAAID,IAAI,EAAE;MACNR,MAAM,CAACC,cAAc,CAACZ,IAAI,EAAE,MAAM,EAAE;QAChCa,KAAK,EAAEM,IAAI;QACXL,QAAQ,EAAE,KAAK;QACfC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE;OACf,CAAC;IACN;EACJ;EAEA,OAAOhB,IAAI;AACf;AAEA,SAASS,UAAUA,CAACN,CAAe,EAAEsB,GAAW,EAAEZ,KAAa;EAC3DF,MAAM,CAACC,cAAc,CAACT,CAAC,EAAEsB,GAAG,EAAE;IAC1BZ,KAAK;IACLC,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE;GACf,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}