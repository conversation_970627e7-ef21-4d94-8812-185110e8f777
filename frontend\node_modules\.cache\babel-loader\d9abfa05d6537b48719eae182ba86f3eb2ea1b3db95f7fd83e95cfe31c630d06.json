{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\OutSource\\\\CV\\\\frontend\\\\src\\\\pages\\\\SignUp.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SignUp = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    studentId: '',\n    department: '',\n    year: '',\n    phone: ''\n  });\n  const [errors, setErrors] = useState({});\n  const {\n    register,\n    loading\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name) {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.length < 2) {\n      newErrors.name = 'Name must be at least 2 characters';\n    }\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    if (formData.year && (formData.year < 1 || formData.year > 4)) {\n      newErrors.year = 'Year must be between 1 and 4';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    const {\n      confirmPassword,\n      ...registrationData\n    } = formData;\n\n    // Convert year to number if provided\n    if (registrationData.year) {\n      registrationData.year = parseInt(registrationData.year);\n    }\n    const result = await register(registrationData);\n    if (result.success) {\n      navigate('/dashboard');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"auth-title\",\n          children: \"Create Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-subtitle\",\n          children: \"Join our CV management system\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"name\",\n            className: \"form-label\",\n            children: \"Full Name *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"name\",\n            name: \"name\",\n            value: formData.name,\n            onChange: handleChange,\n            className: `form-input ${errors.name ? 'error' : ''}`,\n            placeholder: \"Enter your full name\",\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"form-error\",\n            children: errors.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            className: \"form-label\",\n            children: \"Email Address *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleChange,\n            className: `form-input ${errors.email ? 'error' : ''}`,\n            placeholder: \"Enter your email\",\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"form-error\",\n            children: errors.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2\",\n          style: {\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"form-label\",\n              children: \"Password *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              id: \"password\",\n              name: \"password\",\n              value: formData.password,\n              onChange: handleChange,\n              className: `form-input ${errors.password ? 'error' : ''}`,\n              placeholder: \"Enter password\",\n              disabled: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-error\",\n              children: errors.password\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"confirmPassword\",\n              className: \"form-label\",\n              children: \"Confirm Password *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"password\",\n              id: \"confirmPassword\",\n              name: \"confirmPassword\",\n              value: formData.confirmPassword,\n              onChange: handleChange,\n              className: `form-input ${errors.confirmPassword ? 'error' : ''}`,\n              placeholder: \"Confirm password\",\n              disabled: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-error\",\n              children: errors.confirmPassword\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2\",\n          style: {\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"studentId\",\n              className: \"form-label\",\n              children: \"Student ID\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"studentId\",\n              name: \"studentId\",\n              value: formData.studentId,\n              onChange: handleChange,\n              className: \"form-input\",\n              placeholder: \"Enter student ID\",\n              disabled: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"year\",\n              className: \"form-label\",\n              children: \"Year\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"year\",\n              name: \"year\",\n              value: formData.year,\n              onChange: handleChange,\n              className: `form-input ${errors.year ? 'error' : ''}`,\n              disabled: loading,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"1\",\n                children: \"1st Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"2\",\n                children: \"2nd Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"3\",\n                children: \"3rd Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"4\",\n                children: \"4th Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), errors.year && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-error\",\n              children: errors.year\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"department\",\n            className: \"form-label\",\n            children: \"Department\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"department\",\n            name: \"department\",\n            value: formData.department,\n            onChange: handleChange,\n            className: \"form-input\",\n            placeholder: \"Enter your department\",\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"phone\",\n            className: \"form-label\",\n            children: \"Phone Number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"tel\",\n            id: \"phone\",\n            name: \"phone\",\n            value: formData.phone,\n            onChange: handleChange,\n            className: \"form-input\",\n            placeholder: \"Enter your phone number\",\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: loading,\n          style: {\n            width: '100%'\n          },\n          children: loading ? 'Creating Account...' : 'Create Account'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-4\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/signin\",\n            className: \"auth-link\",\n            children: \"Sign in here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s(SignUp, \"Z8Z7dlS7QfeumQUi1LQ3zUSPQzE=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = SignUp;\nexport default SignUp;\nvar _c;\n$RefreshReg$(_c, \"SignUp\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "SignUp", "_s", "formData", "setFormData", "name", "email", "password", "confirmPassword", "studentId", "department", "year", "phone", "errors", "setErrors", "register", "loading", "navigate", "handleChange", "e", "value", "target", "prev", "validateForm", "newErrors", "length", "test", "Object", "keys", "handleSubmit", "preventDefault", "registrationData", "parseInt", "result", "success", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "disabled", "style", "gap", "width", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/OutSource/CV/frontend/src/pages/SignUp.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst SignUp = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    studentId: '',\n    department: '',\n    year: '',\n    phone: ''\n  });\n  const [errors, setErrors] = useState({});\n  const { register, loading } = useAuth();\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.name) {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.length < 2) {\n      newErrors.name = 'Name must be at least 2 characters';\n    }\n\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n\n    if (formData.year && (formData.year < 1 || formData.year > 4)) {\n      newErrors.year = 'Year must be between 1 and 4';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    const { confirmPassword, ...registrationData } = formData;\n    \n    // Convert year to number if provided\n    if (registrationData.year) {\n      registrationData.year = parseInt(registrationData.year);\n    }\n\n    const result = await register(registrationData);\n    \n    if (result.success) {\n      navigate('/dashboard');\n    }\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-card\">\n        <div className=\"auth-header\">\n          <h1 className=\"auth-title\">Create Account</h1>\n          <p className=\"auth-subtitle\">Join our CV management system</p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"form-group\">\n            <label htmlFor=\"name\" className=\"form-label\">\n              Full Name *\n            </label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              name=\"name\"\n              value={formData.name}\n              onChange={handleChange}\n              className={`form-input ${errors.name ? 'error' : ''}`}\n              placeholder=\"Enter your full name\"\n              disabled={loading}\n            />\n            {errors.name && (\n              <p className=\"form-error\">{errors.name}</p>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"email\" className=\"form-label\">\n              Email Address *\n            </label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleChange}\n              className={`form-input ${errors.email ? 'error' : ''}`}\n              placeholder=\"Enter your email\"\n              disabled={loading}\n            />\n            {errors.email && (\n              <p className=\"form-error\">{errors.email}</p>\n            )}\n          </div>\n\n          <div className=\"grid grid-cols-2\" style={{ gap: '1rem' }}>\n            <div className=\"form-group\">\n              <label htmlFor=\"password\" className=\"form-label\">\n                Password *\n              </label>\n              <input\n                type=\"password\"\n                id=\"password\"\n                name=\"password\"\n                value={formData.password}\n                onChange={handleChange}\n                className={`form-input ${errors.password ? 'error' : ''}`}\n                placeholder=\"Enter password\"\n                disabled={loading}\n              />\n              {errors.password && (\n                <p className=\"form-error\">{errors.password}</p>\n              )}\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"confirmPassword\" className=\"form-label\">\n                Confirm Password *\n              </label>\n              <input\n                type=\"password\"\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                value={formData.confirmPassword}\n                onChange={handleChange}\n                className={`form-input ${errors.confirmPassword ? 'error' : ''}`}\n                placeholder=\"Confirm password\"\n                disabled={loading}\n              />\n              {errors.confirmPassword && (\n                <p className=\"form-error\">{errors.confirmPassword}</p>\n              )}\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-2\" style={{ gap: '1rem' }}>\n            <div className=\"form-group\">\n              <label htmlFor=\"studentId\" className=\"form-label\">\n                Student ID\n              </label>\n              <input\n                type=\"text\"\n                id=\"studentId\"\n                name=\"studentId\"\n                value={formData.studentId}\n                onChange={handleChange}\n                className=\"form-input\"\n                placeholder=\"Enter student ID\"\n                disabled={loading}\n              />\n            </div>\n\n            <div className=\"form-group\">\n              <label htmlFor=\"year\" className=\"form-label\">\n                Year\n              </label>\n              <select\n                id=\"year\"\n                name=\"year\"\n                value={formData.year}\n                onChange={handleChange}\n                className={`form-input ${errors.year ? 'error' : ''}`}\n                disabled={loading}\n              >\n                <option value=\"\">Select year</option>\n                <option value=\"1\">1st Year</option>\n                <option value=\"2\">2nd Year</option>\n                <option value=\"3\">3rd Year</option>\n                <option value=\"4\">4th Year</option>\n              </select>\n              {errors.year && (\n                <p className=\"form-error\">{errors.year}</p>\n              )}\n            </div>\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"department\" className=\"form-label\">\n              Department\n            </label>\n            <input\n              type=\"text\"\n              id=\"department\"\n              name=\"department\"\n              value={formData.department}\n              onChange={handleChange}\n              className=\"form-input\"\n              placeholder=\"Enter your department\"\n              disabled={loading}\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"phone\" className=\"form-label\">\n              Phone Number\n            </label>\n            <input\n              type=\"tel\"\n              id=\"phone\"\n              name=\"phone\"\n              value={formData.phone}\n              onChange={handleChange}\n              className=\"form-input\"\n              placeholder=\"Enter your phone number\"\n              disabled={loading}\n            />\n          </div>\n\n          <button\n            type=\"submit\"\n            className=\"btn btn-primary\"\n            disabled={loading}\n            style={{ width: '100%' }}\n          >\n            {loading ? 'Creating Account...' : 'Create Account'}\n          </button>\n        </form>\n\n        <div className=\"text-center mt-4\">\n          <p>\n            Already have an account?{' '}\n            <Link to=\"/signin\" className=\"auth-link\">\n              Sign in here\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SignUp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM;IAAEoB,QAAQ;IAAEC;EAAQ,CAAC,GAAGlB,OAAO,CAAC,CAAC;EACvC,MAAMmB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAE9B,MAAMqB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEd,IAAI;MAAEe;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCjB,WAAW,CAACkB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACjB,IAAI,GAAGe;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAIP,MAAM,CAACR,IAAI,CAAC,EAAE;MAChBS,SAAS,CAACQ,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACjB,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACrB,QAAQ,CAACE,IAAI,EAAE;MAClBmB,SAAS,CAACnB,IAAI,GAAG,kBAAkB;IACrC,CAAC,MAAM,IAAIF,QAAQ,CAACE,IAAI,CAACoB,MAAM,GAAG,CAAC,EAAE;MACnCD,SAAS,CAACnB,IAAI,GAAG,oCAAoC;IACvD;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,EAAE;MACnBkB,SAAS,CAAClB,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACoB,IAAI,CAACvB,QAAQ,CAACG,KAAK,CAAC,EAAE;MAC/CkB,SAAS,CAAClB,KAAK,GAAG,kBAAkB;IACtC;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;MACtBiB,SAAS,CAACjB,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIJ,QAAQ,CAACI,QAAQ,CAACkB,MAAM,GAAG,CAAC,EAAE;MACvCD,SAAS,CAACjB,QAAQ,GAAG,wCAAwC;IAC/D;IAEA,IAAI,CAACJ,QAAQ,CAACK,eAAe,EAAE;MAC7BgB,SAAS,CAAChB,eAAe,GAAG,8BAA8B;IAC5D,CAAC,MAAM,IAAIL,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MACzDgB,SAAS,CAAChB,eAAe,GAAG,wBAAwB;IACtD;IAEA,IAAIL,QAAQ,CAACQ,IAAI,KAAKR,QAAQ,CAACQ,IAAI,GAAG,CAAC,IAAIR,QAAQ,CAACQ,IAAI,GAAG,CAAC,CAAC,EAAE;MAC7Da,SAAS,CAACb,IAAI,GAAG,8BAA8B;IACjD;IAEAG,SAAS,CAACU,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACC,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOV,CAAC,IAAK;IAChCA,CAAC,CAACW,cAAc,CAAC,CAAC;IAElB,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,MAAM;MAAEf,eAAe;MAAE,GAAGuB;IAAiB,CAAC,GAAG5B,QAAQ;;IAEzD;IACA,IAAI4B,gBAAgB,CAACpB,IAAI,EAAE;MACzBoB,gBAAgB,CAACpB,IAAI,GAAGqB,QAAQ,CAACD,gBAAgB,CAACpB,IAAI,CAAC;IACzD;IAEA,MAAMsB,MAAM,GAAG,MAAMlB,QAAQ,CAACgB,gBAAgB,CAAC;IAE/C,IAAIE,MAAM,CAACC,OAAO,EAAE;MAClBjB,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,oBACEjB,OAAA;IAAKmC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BpC,OAAA;MAAKmC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBpC,OAAA;QAAKmC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpC,OAAA;UAAImC,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9CxC,OAAA;UAAGmC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eAENxC,OAAA;QAAMyC,QAAQ,EAAEZ,YAAa;QAACM,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACjDpC,OAAA;UAAKmC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpC,OAAA;YAAO0C,OAAO,EAAC,MAAM;YAACP,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE7C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRxC,OAAA;YACE2C,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,MAAM;YACTvC,IAAI,EAAC,MAAM;YACXe,KAAK,EAAEjB,QAAQ,CAACE,IAAK;YACrBwC,QAAQ,EAAE3B,YAAa;YACvBiB,SAAS,EAAE,cAActB,MAAM,CAACR,IAAI,GAAG,OAAO,GAAG,EAAE,EAAG;YACtDyC,WAAW,EAAC,sBAAsB;YAClCC,QAAQ,EAAE/B;UAAQ;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EACD3B,MAAM,CAACR,IAAI,iBACVL,OAAA;YAAGmC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEvB,MAAM,CAACR;UAAI;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC3C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENxC,OAAA;UAAKmC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpC,OAAA;YAAO0C,OAAO,EAAC,OAAO;YAACP,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRxC,OAAA;YACE2C,IAAI,EAAC,OAAO;YACZC,EAAE,EAAC,OAAO;YACVvC,IAAI,EAAC,OAAO;YACZe,KAAK,EAAEjB,QAAQ,CAACG,KAAM;YACtBuC,QAAQ,EAAE3B,YAAa;YACvBiB,SAAS,EAAE,cAActB,MAAM,CAACP,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;YACvDwC,WAAW,EAAC,kBAAkB;YAC9BC,QAAQ,EAAE/B;UAAQ;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EACD3B,MAAM,CAACP,KAAK,iBACXN,OAAA;YAAGmC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEvB,MAAM,CAACP;UAAK;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENxC,OAAA;UAAKmC,SAAS,EAAC,kBAAkB;UAACa,KAAK,EAAE;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAb,QAAA,gBACvDpC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAO0C,OAAO,EAAC,UAAU;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxC,OAAA;cACE2C,IAAI,EAAC,UAAU;cACfC,EAAE,EAAC,UAAU;cACbvC,IAAI,EAAC,UAAU;cACfe,KAAK,EAAEjB,QAAQ,CAACI,QAAS;cACzBsC,QAAQ,EAAE3B,YAAa;cACvBiB,SAAS,EAAE,cAActB,MAAM,CAACN,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;cAC1DuC,WAAW,EAAC,gBAAgB;cAC5BC,QAAQ,EAAE/B;YAAQ;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EACD3B,MAAM,CAACN,QAAQ,iBACdP,OAAA;cAAGmC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEvB,MAAM,CAACN;YAAQ;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC/C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAO0C,OAAO,EAAC,iBAAiB;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAExD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxC,OAAA;cACE2C,IAAI,EAAC,UAAU;cACfC,EAAE,EAAC,iBAAiB;cACpBvC,IAAI,EAAC,iBAAiB;cACtBe,KAAK,EAAEjB,QAAQ,CAACK,eAAgB;cAChCqC,QAAQ,EAAE3B,YAAa;cACvBiB,SAAS,EAAE,cAActB,MAAM,CAACL,eAAe,GAAG,OAAO,GAAG,EAAE,EAAG;cACjEsC,WAAW,EAAC,kBAAkB;cAC9BC,QAAQ,EAAE/B;YAAQ;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EACD3B,MAAM,CAACL,eAAe,iBACrBR,OAAA;cAAGmC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEvB,MAAM,CAACL;YAAe;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACtD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxC,OAAA;UAAKmC,SAAS,EAAC,kBAAkB;UAACa,KAAK,EAAE;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAb,QAAA,gBACvDpC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAO0C,OAAO,EAAC,WAAW;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxC,OAAA;cACE2C,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,WAAW;cACdvC,IAAI,EAAC,WAAW;cAChBe,KAAK,EAAEjB,QAAQ,CAACM,SAAU;cAC1BoC,QAAQ,EAAE3B,YAAa;cACvBiB,SAAS,EAAC,YAAY;cACtBW,WAAW,EAAC,kBAAkB;cAC9BC,QAAQ,EAAE/B;YAAQ;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENxC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpC,OAAA;cAAO0C,OAAO,EAAC,MAAM;cAACP,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxC,OAAA;cACE4C,EAAE,EAAC,MAAM;cACTvC,IAAI,EAAC,MAAM;cACXe,KAAK,EAAEjB,QAAQ,CAACQ,IAAK;cACrBkC,QAAQ,EAAE3B,YAAa;cACvBiB,SAAS,EAAE,cAActB,MAAM,CAACF,IAAI,GAAG,OAAO,GAAG,EAAE,EAAG;cACtDoC,QAAQ,EAAE/B,OAAQ;cAAAoB,QAAA,gBAElBpC,OAAA;gBAAQoB,KAAK,EAAC,EAAE;gBAAAgB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrCxC,OAAA;gBAAQoB,KAAK,EAAC,GAAG;gBAAAgB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCxC,OAAA;gBAAQoB,KAAK,EAAC,GAAG;gBAAAgB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCxC,OAAA;gBAAQoB,KAAK,EAAC,GAAG;gBAAAgB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCxC,OAAA;gBAAQoB,KAAK,EAAC,GAAG;gBAAAgB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,EACR3B,MAAM,CAACF,IAAI,iBACVX,OAAA;cAAGmC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEvB,MAAM,CAACF;YAAI;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxC,OAAA;UAAKmC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpC,OAAA;YAAO0C,OAAO,EAAC,YAAY;YAACP,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRxC,OAAA;YACE2C,IAAI,EAAC,MAAM;YACXC,EAAE,EAAC,YAAY;YACfvC,IAAI,EAAC,YAAY;YACjBe,KAAK,EAAEjB,QAAQ,CAACO,UAAW;YAC3BmC,QAAQ,EAAE3B,YAAa;YACvBiB,SAAS,EAAC,YAAY;YACtBW,WAAW,EAAC,uBAAuB;YACnCC,QAAQ,EAAE/B;UAAQ;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxC,OAAA;UAAKmC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBpC,OAAA;YAAO0C,OAAO,EAAC,OAAO;YAACP,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRxC,OAAA;YACE2C,IAAI,EAAC,KAAK;YACVC,EAAE,EAAC,OAAO;YACVvC,IAAI,EAAC,OAAO;YACZe,KAAK,EAAEjB,QAAQ,CAACS,KAAM;YACtBiC,QAAQ,EAAE3B,YAAa;YACvBiB,SAAS,EAAC,YAAY;YACtBW,WAAW,EAAC,yBAAyB;YACrCC,QAAQ,EAAE/B;UAAQ;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxC,OAAA;UACE2C,IAAI,EAAC,QAAQ;UACbR,SAAS,EAAC,iBAAiB;UAC3BY,QAAQ,EAAE/B,OAAQ;UAClBgC,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAO,CAAE;UAAAd,QAAA,EAExBpB,OAAO,GAAG,qBAAqB,GAAG;QAAgB;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPxC,OAAA;QAAKmC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BpC,OAAA;UAAAoC,QAAA,GAAG,0BACuB,EAAC,GAAG,eAC5BpC,OAAA,CAACJ,IAAI;YAACuD,EAAE,EAAC,SAAS;YAAChB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAEzC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtC,EAAA,CA3QID,MAAM;EAAA,QAYoBH,OAAO,EACpBD,WAAW;AAAA;AAAAuD,EAAA,GAbxBnD,MAAM;AA6QZ,eAAeA,MAAM;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}