{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\OutSource\\\\CV\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport { useAuth } from './contexts/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Header from './components/Header';\nimport Footer from './components/Footer';\nimport LoadingSpinner from './components/LoadingSpinner';\n\n// Pages\nimport SignIn from './pages/SignIn';\nimport SignUp from './pages/SignUp';\nimport Dashboard from './pages/Dashboard';\nimport CVUpload from './pages/CVUpload';\nimport Profile from './pages/Profile';\nimport News from './pages/News';\nimport NewsDetail from './pages/NewsDetail';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [user && /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 18\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"main-content\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/signin\",\n            element: user ? /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 31\n            }, this) : /*#__PURE__*/_jsxDEV(SignIn, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 62\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/signup\",\n            element: user ? /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 31\n            }, this) : /*#__PURE__*/_jsxDEV(SignUp, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 62\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/upload-cv\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(CVUpload, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/profile\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/news\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(News, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/news/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(NewsDetail, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: user ? /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 24\n            }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/signin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 55\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: user ? /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 24\n            }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/signin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 55\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), user && /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 18\n      }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n        position: \"top-right\",\n        autoClose: 5000,\n        hideProgressBar: false,\n        newestOnTop: false,\n        closeOnClick: true,\n        rtl: false,\n        pauseOnFocusLoss: true,\n        draggable: true,\n        pauseOnHover: true,\n        theme: \"light\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"EmJkapf7qiLC5Br5eCoEq4veZes=\", false, function () {\n  return [useAuth];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "ToastContainer", "useAuth", "ProtectedRoute", "Header", "Footer", "LoadingSpinner", "SignIn", "SignUp", "Dashboard", "CVUpload", "Profile", "News", "NewsDetail", "jsxDEV", "_jsxDEV", "App", "_s", "user", "loading", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "className", "path", "element", "to", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/OutSource/CV/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\n\nimport { useAuth } from './contexts/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Header from './components/Header';\nimport Footer from './components/Footer';\nimport LoadingSpinner from './components/LoadingSpinner';\n\n// Pages\nimport SignIn from './pages/SignIn';\nimport SignUp from './pages/SignUp';\nimport Dashboard from './pages/Dashboard';\nimport CVUpload from './pages/CVUpload';\nimport Profile from './pages/Profile';\nimport News from './pages/News';\nimport NewsDetail from './pages/NewsDetail';\n\nimport './App.css';\n\nfunction App() {\n  const { user, loading } = useAuth();\n\n  if (loading) {\n    return <LoadingSpinner />;\n  }\n\n  return (\n    <Router>\n      <div className=\"App\">\n        {user && <Header />}\n        \n        <main className=\"main-content\">\n          <Routes>\n            {/* Public Routes */}\n            <Route \n              path=\"/signin\" \n              element={user ? <Navigate to=\"/dashboard\" /> : <SignIn />} \n            />\n            <Route \n              path=\"/signup\" \n              element={user ? <Navigate to=\"/dashboard\" /> : <SignUp />} \n            />\n            \n            {/* Protected Routes */}\n            <Route \n              path=\"/dashboard\" \n              element={\n                <ProtectedRoute>\n                  <Dashboard />\n                </ProtectedRoute>\n              } \n            />\n            <Route \n              path=\"/upload-cv\" \n              element={\n                <ProtectedRoute>\n                  <CVUpload />\n                </ProtectedRoute>\n              } \n            />\n            <Route \n              path=\"/profile\" \n              element={\n                <ProtectedRoute>\n                  <Profile />\n                </ProtectedRoute>\n              } \n            />\n            <Route \n              path=\"/news\" \n              element={\n                <ProtectedRoute>\n                  <News />\n                </ProtectedRoute>\n              } \n            />\n            <Route \n              path=\"/news/:id\" \n              element={\n                <ProtectedRoute>\n                  <NewsDetail />\n                </ProtectedRoute>\n              } \n            />\n            \n            {/* Default Route */}\n            <Route \n              path=\"/\" \n              element={\n                user ? <Navigate to=\"/dashboard\" /> : <Navigate to=\"/signin\" />\n              } \n            />\n            \n            {/* Catch all route */}\n            <Route \n              path=\"*\" \n              element={\n                user ? <Navigate to=\"/dashboard\" /> : <Navigate to=\"/signin\" />\n              } \n            />\n          </Routes>\n        </main>\n        \n        {user && <Footer />}\n        \n        <ToastContainer\n          position=\"top-right\"\n          autoClose={5000}\n          hideProgressBar={false}\n          newestOnTop={false}\n          closeOnClick\n          rtl={false}\n          pauseOnFocusLoss\n          draggable\n          pauseOnHover\n          theme=\"light\"\n        />\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAE9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,cAAc,MAAM,6BAA6B;;AAExD;AACA,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,UAAU,MAAM,oBAAoB;AAE3C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAEnC,IAAIiB,OAAO,EAAE;IACX,oBAAOJ,OAAA,CAACT,cAAc;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC3B;EAEA,oBACER,OAAA,CAAClB,MAAM;IAAA2B,QAAA,eACLT,OAAA;MAAKU,SAAS,EAAC,KAAK;MAAAD,QAAA,GACjBN,IAAI,iBAAIH,OAAA,CAACX,MAAM;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEnBR,OAAA;QAAMU,SAAS,EAAC,cAAc;QAAAD,QAAA,eAC5BT,OAAA,CAACjB,MAAM;UAAA0B,QAAA,gBAELT,OAAA,CAAChB,KAAK;YACJ2B,IAAI,EAAC,SAAS;YACdC,OAAO,EAAET,IAAI,gBAAGH,OAAA,CAACf,QAAQ;cAAC4B,EAAE,EAAC;YAAY;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGR,OAAA,CAACR,MAAM;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACFR,OAAA,CAAChB,KAAK;YACJ2B,IAAI,EAAC,SAAS;YACdC,OAAO,EAAET,IAAI,gBAAGH,OAAA,CAACf,QAAQ;cAAC4B,EAAE,EAAC;YAAY;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGR,OAAA,CAACP,MAAM;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eAGFR,OAAA,CAAChB,KAAK;YACJ2B,IAAI,EAAC,YAAY;YACjBC,OAAO,eACLZ,OAAA,CAACZ,cAAc;cAAAqB,QAAA,eACbT,OAAA,CAACN,SAAS;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFR,OAAA,CAAChB,KAAK;YACJ2B,IAAI,EAAC,YAAY;YACjBC,OAAO,eACLZ,OAAA,CAACZ,cAAc;cAAAqB,QAAA,eACbT,OAAA,CAACL,QAAQ;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFR,OAAA,CAAChB,KAAK;YACJ2B,IAAI,EAAC,UAAU;YACfC,OAAO,eACLZ,OAAA,CAACZ,cAAc;cAAAqB,QAAA,eACbT,OAAA,CAACJ,OAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFR,OAAA,CAAChB,KAAK;YACJ2B,IAAI,EAAC,OAAO;YACZC,OAAO,eACLZ,OAAA,CAACZ,cAAc;cAAAqB,QAAA,eACbT,OAAA,CAACH,IAAI;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFR,OAAA,CAAChB,KAAK;YACJ2B,IAAI,EAAC,WAAW;YAChBC,OAAO,eACLZ,OAAA,CAACZ,cAAc;cAAAqB,QAAA,eACbT,OAAA,CAACF,UAAU;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFR,OAAA,CAAChB,KAAK;YACJ2B,IAAI,EAAC,GAAG;YACRC,OAAO,EACLT,IAAI,gBAAGH,OAAA,CAACf,QAAQ;cAAC4B,EAAE,EAAC;YAAY;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGR,OAAA,CAACf,QAAQ;cAAC4B,EAAE,EAAC;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAC/D;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFR,OAAA,CAAChB,KAAK;YACJ2B,IAAI,EAAC,GAAG;YACRC,OAAO,EACLT,IAAI,gBAAGH,OAAA,CAACf,QAAQ;cAAC4B,EAAE,EAAC;YAAY;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGR,OAAA,CAACf,QAAQ;cAAC4B,EAAE,EAAC;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAC/D;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAENL,IAAI,iBAAIH,OAAA,CAACV,MAAM;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEnBR,OAAA,CAACd,cAAc;QACb4B,QAAQ,EAAC,WAAW;QACpBC,SAAS,EAAE,IAAK;QAChBC,eAAe,EAAE,KAAM;QACvBC,WAAW,EAAE,KAAM;QACnBC,YAAY;QACZC,GAAG,EAAE,KAAM;QACXC,gBAAgB;QAChBC,SAAS;QACTC,YAAY;QACZC,KAAK,EAAC;MAAO;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACN,EAAA,CArGQD,GAAG;EAAA,QACgBd,OAAO;AAAA;AAAAqC,EAAA,GAD1BvB,GAAG;AAuGZ,eAAeA,GAAG;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}