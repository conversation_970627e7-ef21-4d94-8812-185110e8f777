{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\OutSource\\\\CV\\\\frontend\\\\src\\\\components\\\\LoadingSpinner.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = ({\n  size = 'medium',\n  text = 'Loading...'\n}) => {\n  const sizeClasses = {\n    small: 'w-6 h-6',\n    medium: 'w-10 h-10',\n    large: 'w-16 h-16'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `spinner ${sizeClasses[size]}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), text && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"loading-text\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 16\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LoadingSpinner", "size", "text", "sizeClasses", "small", "medium", "large", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/OutSource/CV/frontend/src/components/LoadingSpinner.js"], "sourcesContent": ["import React from 'react';\n\nconst LoadingSpinner = ({ size = 'medium', text = 'Loading...' }) => {\n  const sizeClasses = {\n    small: 'w-6 h-6',\n    medium: 'w-10 h-10',\n    large: 'w-16 h-16'\n  };\n\n  return (\n    <div className=\"loading-container\">\n      <div className={`spinner ${sizeClasses[size]}`}></div>\n      {text && <span className=\"loading-text\">{text}</span>}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAC;EAAEC,IAAI,GAAG,QAAQ;EAAEC,IAAI,GAAG;AAAa,CAAC,KAAK;EACnE,MAAMC,WAAW,GAAG;IAClBC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE,WAAW;IACnBC,KAAK,EAAE;EACT,CAAC;EAED,oBACEP,OAAA;IAAKQ,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCT,OAAA;MAAKQ,SAAS,EAAE,WAAWJ,WAAW,CAACF,IAAI,CAAC;IAAG;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACrDV,IAAI,iBAAIH,OAAA;MAAMQ,SAAS,EAAC,cAAc;MAAAC,QAAA,EAAEN;IAAI;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClD,CAAC;AAEV,CAAC;AAACC,EAAA,GAbIb,cAAc;AAepB,eAAeA,cAAc;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}