import React, { useState } from 'react';
import { FiCalendar, FiClock, FiMapPin, FiUsers, FiVideo, FiMic, FiUser } from 'react-icons/fi';

const Sessions = () => {
  const [selectedType, setSelectedType] = useState('all');

  const sessions = [
    {
      id: 1,
      title: 'Career Guidance Workshop',
      type: 'workshop',
      date: '2024-01-15',
      time: '10:00 AM - 12:00 PM',
      location: 'Main Auditorium',
      mode: 'in-person',
      instructor: 'Dr. <PERSON>',
      department: 'Career Services',
      capacity: 150,
      registered: 89,
      description: 'Learn essential career planning strategies, resume building, and interview preparation techniques.',
      topics: ['Resume Writing', 'Interview Skills', 'Career Planning', 'Networking'],
      image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
      id: 2,
      title: 'Research Methodology Seminar',
      type: 'seminar',
      date: '2024-01-18',
      time: '2:00 PM - 4:00 PM',
      location: 'Online',
      mode: 'virtual',
      instructor: 'Prof. <PERSON>',
      department: 'Research Department',
      capacity: 200,
      registered: 156,
      description: 'Advanced research methodologies for undergraduate and graduate students.',
      topics: ['Research Design', 'Data Collection', 'Analysis Methods', 'Academic Writing'],
      image: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
      id: 3,
      title: 'Entrepreneurship Bootcamp',
      type: 'bootcamp',
      date: '2024-01-22',
      time: '9:00 AM - 5:00 PM',
      location: 'Innovation Hub',
      mode: 'in-person',
      instructor: 'Sarah Martinez',
      department: 'Business School',
      capacity: 50,
      registered: 42,
      description: 'Intensive one-day bootcamp covering startup fundamentals and business planning.',
      topics: ['Business Models', 'Funding', 'Marketing', 'Legal Aspects'],
      image: 'https://images.unsplash.com/photo-**********-9303baea8ebd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
      id: 4,
      title: 'Mental Health & Wellness Session',
      type: 'wellness',
      date: '2024-01-25',
      time: '11:00 AM - 12:30 PM',
      location: 'Student Center',
      mode: 'hybrid',
      instructor: 'Dr. Lisa Chen',
      department: 'Student Wellness',
      capacity: 100,
      registered: 67,
      description: 'Learn stress management techniques and maintain mental wellness during studies.',
      topics: ['Stress Management', 'Mindfulness', 'Work-Life Balance', 'Support Resources'],
      image: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
      id: 5,
      title: 'Industry Connect: Tech Leaders Panel',
      type: 'panel',
      date: '2024-01-28',
      time: '3:00 PM - 5:00 PM',
      location: 'Conference Hall',
      mode: 'in-person',
      instructor: 'Multiple Industry Experts',
      department: 'Computer Science',
      capacity: 300,
      registered: 245,
      description: 'Panel discussion with leading tech industry professionals sharing insights and career advice.',
      topics: ['Industry Trends', 'Career Paths', 'Skill Development', 'Q&A Session'],
      image: 'https://images.unsplash.com/photo-1515187029135-18ee286d815b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    },
    {
      id: 6,
      title: 'Academic Writing Workshop',
      type: 'workshop',
      date: '2024-02-01',
      time: '1:00 PM - 3:00 PM',
      location: 'Library Conference Room',
      mode: 'in-person',
      instructor: 'Prof. Robert Johnson',
      department: 'English Department',
      capacity: 80,
      registered: 34,
      description: 'Improve your academic writing skills for research papers and thesis writing.',
      topics: ['Citation Styles', 'Structure', 'Academic Tone', 'Editing Techniques'],
      image: 'https://images.unsplash.com/photo-1456513080510-7bf3a84b82f8?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    }
  ];

  const sessionTypes = [
    { id: 'all', label: 'All Sessions' },
    { id: 'workshop', label: 'Workshops' },
    { id: 'seminar', label: 'Seminars' },
    { id: 'bootcamp', label: 'Bootcamps' },
    { id: 'wellness', label: 'Wellness' },
    { id: 'panel', label: 'Panel Discussions' }
  ];

  const filteredSessions = selectedType === 'all' 
    ? sessions 
    : sessions.filter(session => session.type === selectedType);

  const getTypeColor = (type) => {
    const colors = {
      workshop: 'bg-blue-100 text-blue-800',
      seminar: 'bg-green-100 text-green-800',
      bootcamp: 'bg-purple-100 text-purple-800',
      wellness: 'bg-pink-100 text-pink-800',
      panel: 'bg-orange-100 text-orange-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getModeIcon = (mode) => {
    switch (mode) {
      case 'virtual':
        return <FiVideo className="mode-icon" />;
      case 'hybrid':
        return <FiMic className="mode-icon" />;
      default:
        return <FiMapPin className="mode-icon" />;
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getAvailabilityStatus = (capacity, registered) => {
    const percentage = (registered / capacity) * 100;
    if (percentage >= 90) return { status: 'full', color: 'text-red-500' };
    if (percentage >= 70) return { status: 'filling', color: 'text-yellow-500' };
    return { status: 'available', color: 'text-green-500' };
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <h1 className="page-title">University Sessions</h1>
        <p className="page-subtitle">
          Join our skill development sessions, workshops, and seminars
        </p>
      </div>

      {/* Session Type Filter */}
      <div className="filter-section">
        <div className="category-filters">
          {sessionTypes.map((type) => (
            <button
              key={type.id}
              onClick={() => setSelectedType(type.id)}
              className={`category-btn ${selectedType === type.id ? 'active' : ''}`}
            >
              {type.label}
            </button>
          ))}
        </div>
      </div>

      {/* Sessions Grid */}
      <div className="sessions-grid">
        {filteredSessions.map((session) => {
          const availability = getAvailabilityStatus(session.capacity, session.registered);
          
          return (
            <div key={session.id} className="session-card">
              <div className="session-image">
                <img src={session.image} alt={session.title} />
                <div className="session-overlay">
                  <span className={`type-badge ${getTypeColor(session.type)}`}>
                    {session.type}
                  </span>
                </div>
              </div>
              
              <div className="session-content">
                <div className="session-header">
                  <h3 className="session-title">{session.title}</h3>
                  <div className="session-instructor">
                    <FiUser className="instructor-icon" />
                    <span>{session.instructor}</span>
                  </div>
                  <p className="session-department">{session.department}</p>
                </div>

                <p className="session-description">{session.description}</p>

                <div className="session-details">
                  <div className="detail-item">
                    <FiCalendar className="detail-icon" />
                    <span>{formatDate(session.date)}</span>
                  </div>
                  <div className="detail-item">
                    <FiClock className="detail-icon" />
                    <span>{session.time}</span>
                  </div>
                  <div className="detail-item">
                    {getModeIcon(session.mode)}
                    <span>{session.location}</span>
                  </div>
                </div>

                <div className="session-topics">
                  {session.topics.map((topic, index) => (
                    <span key={index} className="topic-tag">
                      {topic}
                    </span>
                  ))}
                </div>

                <div className="session-stats">
                  <div className="capacity-info">
                    <FiUsers className="capacity-icon" />
                    <span className={availability.color}>
                      {session.registered}/{session.capacity} registered
                    </span>
                  </div>
                  <div className="availability-bar">
                    <div 
                      className="availability-fill"
                      style={{ width: `${(session.registered / session.capacity) * 100}%` }}
                    />
                  </div>
                </div>

                <div className="session-actions">
                  <button 
                    className="btn btn-primary"
                    disabled={session.registered >= session.capacity}
                  >
                    {session.registered >= session.capacity ? 'Full' : 'Register Now'}
                  </button>
                  <button className="btn btn-outline">
                    Learn More
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Stats */}
      <div className="stats-section">
        <h2 className="section-title">Session Statistics</h2>
        <div className="quick-stats">
          <div className="quick-stat">
            <div className="stat-number">24</div>
            <div className="stat-label">Sessions This Month</div>
          </div>
          <div className="quick-stat">
            <div className="stat-number">1,250</div>
            <div className="stat-label">Total Participants</div>
          </div>
          <div className="quick-stat">
            <div className="stat-number">15</div>
            <div className="stat-label">Expert Instructors</div>
          </div>
          <div className="quick-stat">
            <div className="stat-number">4.8</div>
            <div className="stat-label">Average Rating</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sessions;
