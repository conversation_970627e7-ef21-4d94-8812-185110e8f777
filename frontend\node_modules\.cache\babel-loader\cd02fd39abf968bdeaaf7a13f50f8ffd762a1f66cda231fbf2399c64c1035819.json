{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\OutSource\\\\CV\\\\frontend\\\\src\\\\pages\\\\SignIn.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SignIn = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [errors, setErrors] = useState({});\n  const {\n    login,\n    loading\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    const result = await login(formData.email, formData.password);\n    if (result.success) {\n      navigate('/dashboard');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"auth-title\",\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"auth-subtitle\",\n          children: \"Sign in to your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            className: \"form-label\",\n            children: \"Email Address\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleChange,\n            className: `form-input ${errors.email ? 'error' : ''}`,\n            placeholder: \"Enter your email\",\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"form-error\",\n            children: errors.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            className: \"form-label\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleChange,\n            className: `form-input ${errors.password ? 'error' : ''}`,\n            placeholder: \"Enter your password\",\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"form-error\",\n            children: errors.password\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          disabled: loading,\n          style: {\n            width: '100%'\n          },\n          children: loading ? 'Signing In...' : 'Sign In'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-4\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/signup\",\n            className: \"auth-link\",\n            children: \"Sign up here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_s(SignIn, \"2N/Vff81ZJjtZqlCrRjRwF7FXHM=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = SignIn;\nexport default SignIn;\nvar _c;\n$RefreshReg$(_c, \"SignIn\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "SignIn", "_s", "formData", "setFormData", "email", "password", "errors", "setErrors", "login", "loading", "navigate", "handleChange", "e", "name", "value", "target", "prev", "validateForm", "newErrors", "test", "Object", "keys", "length", "handleSubmit", "preventDefault", "result", "success", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "disabled", "style", "width", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/OutSource/CV/frontend/src/pages/SignIn.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst SignIn = () => {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [errors, setErrors] = useState({});\n  const { login, loading } = useAuth();\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    const result = await login(formData.email, formData.password);\n    \n    if (result.success) {\n      navigate('/dashboard');\n    }\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-card\">\n        <div className=\"auth-header\">\n          <h1 className=\"auth-title\">Welcome Back</h1>\n          <p className=\"auth-subtitle\">Sign in to your account</p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"form-group\">\n            <label htmlFor=\"email\" className=\"form-label\">\n              Email Address\n            </label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleChange}\n              className={`form-input ${errors.email ? 'error' : ''}`}\n              placeholder=\"Enter your email\"\n              disabled={loading}\n            />\n            {errors.email && (\n              <p className=\"form-error\">{errors.email}</p>\n            )}\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"password\" className=\"form-label\">\n              Password\n            </label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleChange}\n              className={`form-input ${errors.password ? 'error' : ''}`}\n              placeholder=\"Enter your password\"\n              disabled={loading}\n            />\n            {errors.password && (\n              <p className=\"form-error\">{errors.password}</p>\n            )}\n          </div>\n\n          <button\n            type=\"submit\"\n            className=\"btn btn-primary\"\n            disabled={loading}\n            style={{ width: '100%' }}\n          >\n            {loading ? 'Signing In...' : 'Sign In'}\n          </button>\n        </form>\n\n        <div className=\"text-center mt-4\">\n          <p>\n            Don't have an account?{' '}\n            <Link to=\"/signup\" className=\"auth-link\">\n              Sign up here\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SignIn;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM;IAAEc,KAAK;IAAEC;EAAQ,CAAC,GAAGZ,OAAO,CAAC,CAAC;EACpC,MAAMa,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,MAAMe,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCZ,WAAW,CAACa,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAIR,MAAM,CAACO,IAAI,CAAC,EAAE;MAChBN,SAAS,CAACS,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAChB,QAAQ,CAACE,KAAK,EAAE;MACnBc,SAAS,CAACd,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACe,IAAI,CAACjB,QAAQ,CAACE,KAAK,CAAC,EAAE;MAC/Cc,SAAS,CAACd,KAAK,GAAG,kBAAkB;IACtC;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MACtBa,SAAS,CAACb,QAAQ,GAAG,sBAAsB;IAC7C;IAEAE,SAAS,CAACW,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAElB,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,MAAMQ,MAAM,GAAG,MAAMjB,KAAK,CAACN,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,QAAQ,CAAC;IAE7D,IAAIoB,MAAM,CAACC,OAAO,EAAE;MAClBhB,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,oBACEX,OAAA;IAAK4B,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7B7B,OAAA;MAAK4B,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB7B,OAAA;QAAK4B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7B,OAAA;UAAI4B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5CjC,OAAA;UAAG4B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eAENjC,OAAA;QAAMkC,QAAQ,EAAEV,YAAa;QAACI,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACjD7B,OAAA;UAAK4B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7B,OAAA;YAAOmC,OAAO,EAAC,OAAO;YAACP,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjC,OAAA;YACEoC,IAAI,EAAC,OAAO;YACZC,EAAE,EAAC,OAAO;YACVvB,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEZ,QAAQ,CAACE,KAAM;YACtBiC,QAAQ,EAAE1B,YAAa;YACvBgB,SAAS,EAAE,cAAcrB,MAAM,CAACF,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;YACvDkC,WAAW,EAAC,kBAAkB;YAC9BC,QAAQ,EAAE9B;UAAQ;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EACD1B,MAAM,CAACF,KAAK,iBACXL,OAAA;YAAG4B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEtB,MAAM,CAACF;UAAK;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENjC,OAAA;UAAK4B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB7B,OAAA;YAAOmC,OAAO,EAAC,UAAU;YAACP,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjC,OAAA;YACEoC,IAAI,EAAC,UAAU;YACfC,EAAE,EAAC,UAAU;YACbvB,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEZ,QAAQ,CAACG,QAAS;YACzBgC,QAAQ,EAAE1B,YAAa;YACvBgB,SAAS,EAAE,cAAcrB,MAAM,CAACD,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;YAC1DiC,WAAW,EAAC,qBAAqB;YACjCC,QAAQ,EAAE9B;UAAQ;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EACD1B,MAAM,CAACD,QAAQ,iBACdN,OAAA;YAAG4B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEtB,MAAM,CAACD;UAAQ;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC/C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENjC,OAAA;UACEoC,IAAI,EAAC,QAAQ;UACbR,SAAS,EAAC,iBAAiB;UAC3BY,QAAQ,EAAE9B,OAAQ;UAClB+B,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAb,QAAA,EAExBnB,OAAO,GAAG,eAAe,GAAG;QAAS;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPjC,OAAA;QAAK4B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B7B,OAAA;UAAA6B,QAAA,GAAG,wBACqB,EAAC,GAAG,eAC1B7B,OAAA,CAACJ,IAAI;YAAC+C,EAAE,EAAC,SAAS;YAACf,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAEzC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CA3HID,MAAM;EAAA,QAMiBH,OAAO,EACjBD,WAAW;AAAA;AAAA+C,EAAA,GAPxB3C,MAAM;AA6HZ,eAAeA,MAAM;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}