{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\OutSource\\\\CV\\\\frontend\\\\src\\\\pages\\\\Courses.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { FiPlay, FiClock, FiUsers, FiStar, FiBookmark, FiExternalLink } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Courses = () => {\n  _s();\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const courses = [{\n    id: 1,\n    title: 'Advanced React Development',\n    instructor: '<PERSON>',\n    duration: '8 hours',\n    students: 1250,\n    rating: 4.8,\n    category: 'programming',\n    level: 'Advanced',\n    price: 'Free',\n    image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',\n    description: 'Master advanced React concepts including hooks, context, and performance optimization.',\n    tags: ['React', 'JavaScript', 'Frontend']\n  }, {\n    id: 2,\n    title: 'Data Science Fundamentals',\n    instructor: 'Dr. <PERSON>',\n    duration: '12 hours',\n    students: 890,\n    rating: 4.9,\n    category: 'data-science',\n    level: 'Beginner',\n    price: 'Free',\n    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',\n    description: 'Learn the basics of data science, statistics, and machine learning.',\n    tags: ['Python', 'Statistics', 'ML']\n  }, {\n    id: 3,\n    title: 'UI/UX Design Masterclass',\n    instructor: 'Emma Rodriguez',\n    duration: '10 hours',\n    students: 2100,\n    rating: 4.7,\n    category: 'design',\n    level: 'Intermediate',\n    price: 'Free',\n    image: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',\n    description: 'Create stunning user interfaces and improve user experience design skills.',\n    tags: ['Design', 'Figma', 'Prototyping']\n  }, {\n    id: 4,\n    title: 'Digital Marketing Strategy',\n    instructor: 'Alex Thompson',\n    duration: '6 hours',\n    students: 1580,\n    rating: 4.6,\n    category: 'marketing',\n    level: 'Beginner',\n    price: 'Free',\n    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',\n    description: 'Learn effective digital marketing strategies and social media management.',\n    tags: ['Marketing', 'SEO', 'Social Media']\n  }, {\n    id: 5,\n    title: 'Cloud Computing with AWS',\n    instructor: 'David Kumar',\n    duration: '15 hours',\n    students: 750,\n    rating: 4.8,\n    category: 'cloud',\n    level: 'Intermediate',\n    price: 'Free',\n    image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',\n    description: 'Master cloud computing concepts and AWS services for modern applications.',\n    tags: ['AWS', 'Cloud', 'DevOps']\n  }, {\n    id: 6,\n    title: 'Mobile App Development',\n    instructor: 'Lisa Park',\n    duration: '20 hours',\n    students: 980,\n    rating: 4.7,\n    category: 'programming',\n    level: 'Intermediate',\n    price: 'Free',\n    image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',\n    description: 'Build cross-platform mobile applications using React Native.',\n    tags: ['React Native', 'Mobile', 'JavaScript']\n  }];\n  const categories = [{\n    id: 'all',\n    label: 'All Courses'\n  }, {\n    id: 'programming',\n    label: 'Programming'\n  }, {\n    id: 'data-science',\n    label: 'Data Science'\n  }, {\n    id: 'design',\n    label: 'Design'\n  }, {\n    id: 'marketing',\n    label: 'Marketing'\n  }, {\n    id: 'cloud',\n    label: 'Cloud Computing'\n  }];\n  const filteredCourses = selectedCategory === 'all' ? courses : courses.filter(course => course.category === selectedCategory);\n  const getLevelColor = level => {\n    switch (level) {\n      case 'Beginner':\n        return 'bg-green-100 text-green-800';\n      case 'Intermediate':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'Advanced':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"page-title\",\n        children: \"Courses & Webinars\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"page-subtitle\",\n        children: \"Enhance your skills with our curated collection of courses and webinars\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filter-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"category-filters\",\n        children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setSelectedCategory(category.id),\n          className: `category-btn ${selectedCategory === category.id ? 'active' : ''}`,\n          children: category.label\n        }, category.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"courses-grid\",\n      children: filteredCourses.map(course => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"course-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"course-image\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: course.image,\n            alt: course.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"course-overlay\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"play-btn\",\n              children: /*#__PURE__*/_jsxDEV(FiPlay, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"course-bookmark\",\n            children: /*#__PURE__*/_jsxDEV(FiBookmark, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"course-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"course-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"course-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `level-badge ${getLevelColor(course.level)}`,\n                children: course.level\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"price-badge\",\n                children: course.price\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"course-title\",\n              children: course.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"course-instructor\",\n              children: [\"by \", course.instructor]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"course-description\",\n            children: course.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"course-tags\",\n            children: course.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"course-tag\",\n              children: tag\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"course-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-item\",\n              children: [/*#__PURE__*/_jsxDEV(FiClock, {\n                className: \"stat-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: course.duration\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-item\",\n              children: [/*#__PURE__*/_jsxDEV(FiUsers, {\n                className: \"stat-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: course.students.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-item\",\n              children: [/*#__PURE__*/_jsxDEV(FiStar, {\n                className: \"stat-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: course.rating\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"course-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              children: \"Start Learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-outline\",\n              children: [/*#__PURE__*/_jsxDEV(FiExternalLink, {\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), \"Preview\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, course.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"webinars-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title\",\n        children: \"Upcoming Webinars\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"webinars-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"webinar-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"webinar-date\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-day\",\n              children: \"25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-month\",\n              children: \"Dec\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"webinar-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"webinar-title\",\n              children: \"Career Development in Tech\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"webinar-speaker\",\n              children: \"Speaker: John Smith, Senior Developer at Google\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"webinar-time\",\n              children: \"2:00 PM - 3:30 PM EST\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary btn-sm\",\n              children: \"Register Now\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"webinar-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"webinar-date\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-day\",\n              children: \"28\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-month\",\n              children: \"Dec\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"webinar-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"webinar-title\",\n              children: \"AI and Machine Learning Trends\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"webinar-speaker\",\n              children: \"Speaker: Dr. Maria Garcia, AI Research Scientist\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"webinar-time\",\n              children: \"1:00 PM - 2:30 PM EST\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary btn-sm\",\n              children: \"Register Now\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"webinar-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"webinar-date\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-day\",\n              children: \"02\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"date-month\",\n              children: \"Jan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"webinar-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"webinar-title\",\n              children: \"Building Your Personal Brand\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"webinar-speaker\",\n              children: \"Speaker: Sarah Wilson, Marketing Director\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"webinar-time\",\n              children: \"3:00 PM - 4:00 PM EST\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary btn-sm\",\n              children: \"Register Now\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n};\n_s(Courses, \"ka1F1ceqEXioutdx48zEaS3nBME=\");\n_c = Courses;\nexport default Courses;\nvar _c;\n$RefreshReg$(_c, \"Courses\");", "map": {"version": 3, "names": ["React", "useState", "FiPlay", "<PERSON><PERSON><PERSON>", "FiUsers", "FiStar", "FiBookmark", "FiExternalLink", "jsxDEV", "_jsxDEV", "Courses", "_s", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "courses", "id", "title", "instructor", "duration", "students", "rating", "category", "level", "price", "image", "description", "tags", "categories", "label", "filteredCourses", "filter", "course", "getLevelColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "onClick", "src", "alt", "tag", "index", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/OutSource/CV/frontend/src/pages/Courses.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { FiPlay, FiClock, FiUsers, FiStar, FiBookmark, FiExternalLink } from 'react-icons/fi';\n\nconst Courses = () => {\n  const [selectedCategory, setSelectedCategory] = useState('all');\n\n  const courses = [\n    {\n      id: 1,\n      title: 'Advanced React Development',\n      instructor: '<PERSON>',\n      duration: '8 hours',\n      students: 1250,\n      rating: 4.8,\n      category: 'programming',\n      level: 'Advanced',\n      price: 'Free',\n      image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',\n      description: 'Master advanced React concepts including hooks, context, and performance optimization.',\n      tags: ['React', 'JavaScript', 'Frontend']\n    },\n    {\n      id: 2,\n      title: 'Data Science Fundamentals',\n      instructor: 'Dr. <PERSON>',\n      duration: '12 hours',\n      students: 890,\n      rating: 4.9,\n      category: 'data-science',\n      level: 'Beginner',\n      price: 'Free',\n      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',\n      description: 'Learn the basics of data science, statistics, and machine learning.',\n      tags: ['Python', 'Statistics', 'ML']\n    },\n    {\n      id: 3,\n      title: 'UI/UX Design Masterclass',\n      instructor: 'Emma Rodriguez',\n      duration: '10 hours',\n      students: 2100,\n      rating: 4.7,\n      category: 'design',\n      level: 'Intermediate',\n      price: 'Free',\n      image: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',\n      description: 'Create stunning user interfaces and improve user experience design skills.',\n      tags: ['Design', 'Figma', 'Prototyping']\n    },\n    {\n      id: 4,\n      title: 'Digital Marketing Strategy',\n      instructor: 'Alex Thompson',\n      duration: '6 hours',\n      students: 1580,\n      rating: 4.6,\n      category: 'marketing',\n      level: 'Beginner',\n      price: 'Free',\n      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',\n      description: 'Learn effective digital marketing strategies and social media management.',\n      tags: ['Marketing', 'SEO', 'Social Media']\n    },\n    {\n      id: 5,\n      title: 'Cloud Computing with AWS',\n      instructor: 'David Kumar',\n      duration: '15 hours',\n      students: 750,\n      rating: 4.8,\n      category: 'cloud',\n      level: 'Intermediate',\n      price: 'Free',\n      image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',\n      description: 'Master cloud computing concepts and AWS services for modern applications.',\n      tags: ['AWS', 'Cloud', 'DevOps']\n    },\n    {\n      id: 6,\n      title: 'Mobile App Development',\n      instructor: 'Lisa Park',\n      duration: '20 hours',\n      students: 980,\n      rating: 4.7,\n      category: 'programming',\n      level: 'Intermediate',\n      price: 'Free',\n      image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',\n      description: 'Build cross-platform mobile applications using React Native.',\n      tags: ['React Native', 'Mobile', 'JavaScript']\n    }\n  ];\n\n  const categories = [\n    { id: 'all', label: 'All Courses' },\n    { id: 'programming', label: 'Programming' },\n    { id: 'data-science', label: 'Data Science' },\n    { id: 'design', label: 'Design' },\n    { id: 'marketing', label: 'Marketing' },\n    { id: 'cloud', label: 'Cloud Computing' }\n  ];\n\n  const filteredCourses = selectedCategory === 'all' \n    ? courses \n    : courses.filter(course => course.category === selectedCategory);\n\n  const getLevelColor = (level) => {\n    switch (level) {\n      case 'Beginner':\n        return 'bg-green-100 text-green-800';\n      case 'Intermediate':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'Advanced':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"page-container\">\n      <div className=\"page-header\">\n        <h1 className=\"page-title\">Courses & Webinars</h1>\n        <p className=\"page-subtitle\">\n          Enhance your skills with our curated collection of courses and webinars\n        </p>\n      </div>\n\n      {/* Category Filter */}\n      <div className=\"filter-section\">\n        <div className=\"category-filters\">\n          {categories.map((category) => (\n            <button\n              key={category.id}\n              onClick={() => setSelectedCategory(category.id)}\n              className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}\n            >\n              {category.label}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Courses Grid */}\n      <div className=\"courses-grid\">\n        {filteredCourses.map((course) => (\n          <div key={course.id} className=\"course-card\">\n            <div className=\"course-image\">\n              <img src={course.image} alt={course.title} />\n              <div className=\"course-overlay\">\n                <button className=\"play-btn\">\n                  <FiPlay />\n                </button>\n              </div>\n              <div className=\"course-bookmark\">\n                <FiBookmark />\n              </div>\n            </div>\n            \n            <div className=\"course-content\">\n              <div className=\"course-header\">\n                <div className=\"course-meta\">\n                  <span className={`level-badge ${getLevelColor(course.level)}`}>\n                    {course.level}\n                  </span>\n                  <span className=\"price-badge\">{course.price}</span>\n                </div>\n                <h3 className=\"course-title\">{course.title}</h3>\n                <p className=\"course-instructor\">by {course.instructor}</p>\n              </div>\n\n              <p className=\"course-description\">{course.description}</p>\n\n              <div className=\"course-tags\">\n                {course.tags.map((tag, index) => (\n                  <span key={index} className=\"course-tag\">\n                    {tag}\n                  </span>\n                ))}\n              </div>\n\n              <div className=\"course-stats\">\n                <div className=\"stat-item\">\n                  <FiClock className=\"stat-icon\" />\n                  <span>{course.duration}</span>\n                </div>\n                <div className=\"stat-item\">\n                  <FiUsers className=\"stat-icon\" />\n                  <span>{course.students.toLocaleString()}</span>\n                </div>\n                <div className=\"stat-item\">\n                  <FiStar className=\"stat-icon\" />\n                  <span>{course.rating}</span>\n                </div>\n              </div>\n\n              <div className=\"course-actions\">\n                <button className=\"btn btn-primary\">\n                  Start Learning\n                </button>\n                <button className=\"btn btn-outline\">\n                  <FiExternalLink className=\"mr-2\" />\n                  Preview\n                </button>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Upcoming Webinars Section */}\n      <div className=\"webinars-section\">\n        <h2 className=\"section-title\">Upcoming Webinars</h2>\n        <div className=\"webinars-grid\">\n          <div className=\"webinar-card\">\n            <div className=\"webinar-date\">\n              <div className=\"date-day\">25</div>\n              <div className=\"date-month\">Dec</div>\n            </div>\n            <div className=\"webinar-content\">\n              <h3 className=\"webinar-title\">Career Development in Tech</h3>\n              <p className=\"webinar-speaker\">Speaker: John Smith, Senior Developer at Google</p>\n              <p className=\"webinar-time\">2:00 PM - 3:30 PM EST</p>\n              <button className=\"btn btn-primary btn-sm\">Register Now</button>\n            </div>\n          </div>\n\n          <div className=\"webinar-card\">\n            <div className=\"webinar-date\">\n              <div className=\"date-day\">28</div>\n              <div className=\"date-month\">Dec</div>\n            </div>\n            <div className=\"webinar-content\">\n              <h3 className=\"webinar-title\">AI and Machine Learning Trends</h3>\n              <p className=\"webinar-speaker\">Speaker: Dr. Maria Garcia, AI Research Scientist</p>\n              <p className=\"webinar-time\">1:00 PM - 2:30 PM EST</p>\n              <button className=\"btn btn-primary btn-sm\">Register Now</button>\n            </div>\n          </div>\n\n          <div className=\"webinar-card\">\n            <div className=\"webinar-date\">\n              <div className=\"date-day\">02</div>\n              <div className=\"date-month\">Jan</div>\n            </div>\n            <div className=\"webinar-content\">\n              <h3 className=\"webinar-title\">Building Your Personal Brand</h3>\n              <p className=\"webinar-speaker\">Speaker: Sarah Wilson, Marketing Director</p>\n              <p className=\"webinar-time\">3:00 PM - 4:00 PM EST</p>\n              <button className=\"btn btn-primary btn-sm\">Register Now</button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Courses;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,cAAc,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9F,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMa,OAAO,GAAG,CACd;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE,eAAe;IAC3BC,QAAQ,EAAE,SAAS;IACnBC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,aAAa;IACvBC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,6GAA6G;IACpHC,WAAW,EAAE,wFAAwF;IACrGC,IAAI,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,UAAU;EAC1C,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE,kBAAkB;IAC9BC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,0GAA0G;IACjHC,WAAW,EAAE,qEAAqE;IAClFC,IAAI,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,IAAI;EACrC,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,0BAA0B;IACjCC,UAAU,EAAE,gBAAgB;IAC5BC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,0GAA0G;IACjHC,WAAW,EAAE,4EAA4E;IACzFC,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,aAAa;EACzC,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,4BAA4B;IACnCC,UAAU,EAAE,eAAe;IAC3BC,QAAQ,EAAE,SAAS;IACnBC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,6GAA6G;IACpHC,WAAW,EAAE,2EAA2E;IACxFC,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,EAAE,cAAc;EAC3C,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,0BAA0B;IACjCC,UAAU,EAAE,aAAa;IACzBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,6GAA6G;IACpHC,WAAW,EAAE,2EAA2E;IACxFC,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ;EACjC,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,wBAAwB;IAC/BC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,aAAa;IACvBC,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,6GAA6G;IACpHC,WAAW,EAAE,8DAA8D;IAC3EC,IAAI,EAAE,CAAC,cAAc,EAAE,QAAQ,EAAE,YAAY;EAC/C,CAAC,CACF;EAED,MAAMC,UAAU,GAAG,CACjB;IAAEZ,EAAE,EAAE,KAAK;IAAEa,KAAK,EAAE;EAAc,CAAC,EACnC;IAAEb,EAAE,EAAE,aAAa;IAAEa,KAAK,EAAE;EAAc,CAAC,EAC3C;IAAEb,EAAE,EAAE,cAAc;IAAEa,KAAK,EAAE;EAAe,CAAC,EAC7C;IAAEb,EAAE,EAAE,QAAQ;IAAEa,KAAK,EAAE;EAAS,CAAC,EACjC;IAAEb,EAAE,EAAE,WAAW;IAAEa,KAAK,EAAE;EAAY,CAAC,EACvC;IAAEb,EAAE,EAAE,OAAO;IAAEa,KAAK,EAAE;EAAkB,CAAC,CAC1C;EAED,MAAMC,eAAe,GAAGjB,gBAAgB,KAAK,KAAK,GAC9CE,OAAO,GACPA,OAAO,CAACgB,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACV,QAAQ,KAAKT,gBAAgB,CAAC;EAElE,MAAMoB,aAAa,GAAIV,KAAK,IAAK;IAC/B,QAAQA,KAAK;MACX,KAAK,UAAU;QACb,OAAO,6BAA6B;MACtC,KAAK,cAAc;QACjB,OAAO,+BAA+B;MACxC,KAAK,UAAU;QACb,OAAO,yBAAyB;MAClC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,oBACEb,OAAA;IAAKwB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BzB,OAAA;MAAKwB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BzB,OAAA;QAAIwB,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClD7B,OAAA;QAAGwB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAE7B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BzB,OAAA;QAAKwB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAC9BP,UAAU,CAACY,GAAG,CAAElB,QAAQ,iBACvBZ,OAAA;UAEE+B,OAAO,EAAEA,CAAA,KAAM3B,mBAAmB,CAACQ,QAAQ,CAACN,EAAE,CAAE;UAChDkB,SAAS,EAAE,gBAAgBrB,gBAAgB,KAAKS,QAAQ,CAACN,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAmB,QAAA,EAE7Eb,QAAQ,CAACO;QAAK,GAJVP,QAAQ,CAACN,EAAE;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKV,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1BL,eAAe,CAACU,GAAG,CAAER,MAAM,iBAC1BtB,OAAA;QAAqBwB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1CzB,OAAA;UAAKwB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzB,OAAA;YAAKgC,GAAG,EAAEV,MAAM,CAACP,KAAM;YAACkB,GAAG,EAAEX,MAAM,CAACf;UAAM;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7C7B,OAAA;YAAKwB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BzB,OAAA;cAAQwB,SAAS,EAAC,UAAU;cAAAC,QAAA,eAC1BzB,OAAA,CAACP,MAAM;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN7B,OAAA;YAAKwB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BzB,OAAA,CAACH,UAAU;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7B,OAAA;UAAKwB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BzB,OAAA;YAAKwB,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BzB,OAAA;cAAKwB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BzB,OAAA;gBAAMwB,SAAS,EAAE,eAAeD,aAAa,CAACD,MAAM,CAACT,KAAK,CAAC,EAAG;gBAAAY,QAAA,EAC3DH,MAAM,CAACT;cAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACP7B,OAAA;gBAAMwB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEH,MAAM,CAACR;cAAK;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACN7B,OAAA;cAAIwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEH,MAAM,CAACf;YAAK;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChD7B,OAAA;cAAGwB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,GAAC,KAAG,EAACH,MAAM,CAACd,UAAU;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eAEN7B,OAAA;YAAGwB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAEH,MAAM,CAACN;UAAW;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE1D7B,OAAA;YAAKwB,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBH,MAAM,CAACL,IAAI,CAACa,GAAG,CAAC,CAACI,GAAG,EAAEC,KAAK,kBAC1BnC,OAAA;cAAkBwB,SAAS,EAAC,YAAY;cAAAC,QAAA,EACrCS;YAAG,GADKC,KAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7B,OAAA;YAAKwB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzB,OAAA;cAAKwB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzB,OAAA,CAACN,OAAO;gBAAC8B,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjC7B,OAAA;gBAAAyB,QAAA,EAAOH,MAAM,CAACb;cAAQ;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzB,OAAA,CAACL,OAAO;gBAAC6B,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjC7B,OAAA;gBAAAyB,QAAA,EAAOH,MAAM,CAACZ,QAAQ,CAAC0B,cAAc,CAAC;cAAC;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzB,OAAA,CAACJ,MAAM;gBAAC4B,SAAS,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChC7B,OAAA;gBAAAyB,QAAA,EAAOH,MAAM,CAACX;cAAM;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7B,OAAA;YAAKwB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BzB,OAAA;cAAQwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7B,OAAA;cAAQwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBACjCzB,OAAA,CAACF,cAAc;gBAAC0B,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GA3DEP,MAAM,CAAChB,EAAE;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4Dd,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BzB,OAAA;QAAIwB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpD7B,OAAA;QAAKwB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BzB,OAAA;UAAKwB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzB,OAAA;YAAKwB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzB,OAAA;cAAKwB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClC7B,OAAA;cAAKwB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACN7B,OAAA;YAAKwB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BzB,OAAA;cAAIwB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7D7B,OAAA;cAAGwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAA+C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClF7B,OAAA;cAAGwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrD7B,OAAA;cAAQwB,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7B,OAAA;UAAKwB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzB,OAAA;YAAKwB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzB,OAAA;cAAKwB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClC7B,OAAA;cAAKwB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACN7B,OAAA;YAAKwB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BzB,OAAA;cAAIwB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjE7B,OAAA;cAAGwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAgD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnF7B,OAAA;cAAGwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrD7B,OAAA;cAAQwB,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7B,OAAA;UAAKwB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzB,OAAA;YAAKwB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzB,OAAA;cAAKwB,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClC7B,OAAA;cAAKwB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACN7B,OAAA;YAAKwB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BzB,OAAA;cAAIwB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/D7B,OAAA;cAAGwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5E7B,OAAA;cAAGwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrD7B,OAAA;cAAQwB,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA7PID,OAAO;AAAAoC,EAAA,GAAPpC,OAAO;AA+Pb,eAAeA,OAAO;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}