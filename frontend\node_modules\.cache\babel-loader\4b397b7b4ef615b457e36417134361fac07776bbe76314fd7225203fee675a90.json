{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\OutSource\\\\CV\\\\frontend\\\\src\\\\pages\\\\NewsDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link, useNavigate } from 'react-router-dom';\nimport { FiCalendar, FiEye, FiArrowLeft, FiHeart, FiUser } from 'react-icons/fi';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NewsDetail = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useAuth();\n  const [article, setArticle] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [liking, setLiking] = useState(false);\n  useEffect(() => {\n    fetchArticle();\n  }, [id]);\n  const fetchArticle = async () => {\n    try {\n      const response = await axios.get(`/api/news/${id}`);\n      setArticle(response.data.data);\n    } catch (error) {\n      var _error$response;\n      console.error('Error fetching article:', error);\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 404) {\n        navigate('/news');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLike = async () => {\n    if (!user) return;\n    setLiking(true);\n    try {\n      const response = await axios.post(`/api/news/like/${id}`);\n      setArticle(prev => ({\n        ...prev,\n        isLiked: response.data.data.isLiked,\n        likeCount: response.data.data.likeCount\n      }));\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    } finally {\n      setLiking(false);\n    }\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const getCategoryColor = category => {\n    const colors = {\n      announcement: 'bg-blue-100 text-blue-800',\n      event: 'bg-green-100 text-green-800',\n      deadline: 'bg-red-100 text-red-800',\n      general: 'bg-gray-100 text-gray-800'\n    };\n    return colors[category] || colors.general;\n  };\n  const getPriorityIcon = priority => {\n    switch (priority) {\n      case 'urgent':\n        return '🔴';\n      case 'high':\n        return '🟠';\n      case 'medium':\n        return '🟡';\n      case 'low':\n        return '🟢';\n      default:\n        return '';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      text: \"Loading article...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 12\n    }, this);\n  }\n  if (!article) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-state-icon\",\n          children: \"\\uD83D\\uDCF0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"empty-state-title\",\n          children: \"Article Not Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"empty-state-text\",\n          children: \"The article you're looking for doesn't exist or has been removed.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/news\",\n          className: \"btn btn-primary mt-4\",\n          children: \"Back to News\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/news\",\n        className: \"btn btn-outline btn-sm\",\n        children: [/*#__PURE__*/_jsxDEV(FiArrowLeft, {\n          className: \"mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), \"Back to News\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"article\", {\n      className: \"article-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"article-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"article-meta mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: `news-category ${getCategoryColor(article.category)}`,\n              children: article.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), article.priority !== 'medium' && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"priority-indicator\",\n              children: getPriorityIcon(article.priority)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4 text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1\",\n              children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: formatDate(article.publishDate)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1\",\n              children: [/*#__PURE__*/_jsxDEV(FiEye, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [article.views, \" views\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), article.author && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1\",\n              children: [/*#__PURE__*/_jsxDEV(FiUser, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"by \", article.author.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"article-title\",\n          children: article.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), article.summary && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"article-summary\",\n          children: article.summary\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), article.image && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"article-image-container\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: article.image,\n          alt: article.title,\n          className: \"article-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"article-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"article-content\",\n          children: article.content.split('\\n').map((paragraph, index) => /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: paragraph\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), article.tags && article.tags.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"article-tags\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-gray-700 mb-2\",\n            children: \"Tags:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2\",\n            children: article.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tag\",\n              children: [\"#\", tag]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this), user && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"article-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleLike,\n            disabled: liking,\n            className: `like-button ${article.isLiked ? 'liked' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(FiHeart, {\n              className: article.isLiked ? 'fill-current' : ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [article.likeCount, \" \", article.likeCount === 1 ? 'like' : 'likes']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .article-container {\n          max-width: 800px;\n          margin: 0 auto;\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          overflow: hidden;\n        }\n\n        .article-header {\n          padding: 2rem 2rem 1rem;\n        }\n\n        .news-category {\n          padding: 0.25rem 0.75rem;\n          border-radius: 9999px;\n          font-size: 0.75rem;\n          font-weight: 500;\n          text-transform: capitalize;\n        }\n\n        .priority-indicator {\n          font-size: 0.875rem;\n        }\n\n        .article-title {\n          font-size: 2.5rem;\n          font-weight: bold;\n          line-height: 1.2;\n          color: #1f2937;\n          margin-bottom: 1rem;\n        }\n\n        .article-summary {\n          font-size: 1.25rem;\n          color: #6b7280;\n          line-height: 1.6;\n          font-style: italic;\n        }\n\n        .article-image-container {\n          width: 100%;\n          max-height: 400px;\n          overflow: hidden;\n        }\n\n        .article-image {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n        }\n\n        .article-body {\n          padding: 2rem;\n        }\n\n        .article-content {\n          font-size: 1.125rem;\n          line-height: 1.8;\n          color: #374151;\n          margin-bottom: 2rem;\n        }\n\n        .article-tags {\n          margin-bottom: 2rem;\n          padding-top: 2rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .tag {\n          background: #f3f4f6;\n          color: #6b7280;\n          padding: 0.25rem 0.75rem;\n          border-radius: 9999px;\n          font-size: 0.875rem;\n        }\n\n        .article-actions {\n          padding-top: 2rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .like-button {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          background: none;\n          border: 2px solid #e5e7eb;\n          border-radius: 9999px;\n          padding: 0.75rem 1.5rem;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s ease;\n        }\n\n        .like-button:hover {\n          border-color: #ef4444;\n          color: #ef4444;\n        }\n\n        .like-button.liked {\n          border-color: #ef4444;\n          color: #ef4444;\n          background: #fef2f2;\n        }\n\n        .like-button:disabled {\n          opacity: 0.6;\n          cursor: not-allowed;\n        }\n\n        @media (max-width: 768px) {\n          .article-header {\n            padding: 1.5rem 1.5rem 1rem;\n          }\n\n          .article-body {\n            padding: 1.5rem;\n          }\n\n          .article-title {\n            font-size: 2rem;\n          }\n\n          .article-summary {\n            font-size: 1.125rem;\n          }\n\n          .article-content {\n            font-size: 1rem;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n};\n_s(NewsDetail, \"JehaJrmAPSzup0Gl7Bcdz4u25i8=\", false, function () {\n  return [useParams, useNavigate, useAuth];\n});\n_c = NewsDetail;\nexport default NewsDetail;\nvar _c;\n$RefreshReg$(_c, \"NewsDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "useNavigate", "FiCalendar", "FiEye", "FiArrowLeft", "<PERSON><PERSON><PERSON><PERSON>", "FiUser", "LoadingSpinner", "useAuth", "axios", "jsxDEV", "_jsxDEV", "NewsDetail", "_s", "id", "navigate", "user", "article", "setArticle", "loading", "setLoading", "liking", "setLiking", "fetchArticle", "response", "get", "data", "error", "_error$response", "console", "status", "handleLike", "post", "prev", "isLiked", "likeCount", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getCategoryColor", "category", "colors", "announcement", "event", "deadline", "general", "getPriorityIcon", "priority", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "to", "size", "publishDate", "views", "author", "name", "title", "summary", "image", "src", "alt", "content", "split", "map", "paragraph", "index", "tags", "length", "tag", "onClick", "disabled", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/OutSource/CV/frontend/src/pages/NewsDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';\nimport { FiCalendar, FiEye, FiArrowLeft, FiHeart, FiUser } from 'react-icons/fi';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport { useAuth } from '../contexts/AuthContext';\nimport axios from 'axios';\n\nconst NewsDetail = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { user } = useAuth();\n  const [article, setArticle] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [liking, setLiking] = useState(false);\n\n  useEffect(() => {\n    fetchArticle();\n  }, [id]);\n\n  const fetchArticle = async () => {\n    try {\n      const response = await axios.get(`/api/news/${id}`);\n      setArticle(response.data.data);\n    } catch (error) {\n      console.error('Error fetching article:', error);\n      if (error.response?.status === 404) {\n        navigate('/news');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLike = async () => {\n    if (!user) return;\n\n    setLiking(true);\n    try {\n      const response = await axios.post(`/api/news/like/${id}`);\n      setArticle(prev => ({\n        ...prev,\n        isLiked: response.data.data.isLiked,\n        likeCount: response.data.data.likeCount\n      }));\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    } finally {\n      setLiking(false);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const getCategoryColor = (category) => {\n    const colors = {\n      announcement: 'bg-blue-100 text-blue-800',\n      event: 'bg-green-100 text-green-800',\n      deadline: 'bg-red-100 text-red-800',\n      general: 'bg-gray-100 text-gray-800'\n    };\n    return colors[category] || colors.general;\n  };\n\n  const getPriorityIcon = (priority) => {\n    switch (priority) {\n      case 'urgent':\n        return '🔴';\n      case 'high':\n        return '🟠';\n      case 'medium':\n        return '🟡';\n      case 'low':\n        return '🟢';\n      default:\n        return '';\n    }\n  };\n\n  if (loading) {\n    return <LoadingSpinner text=\"Loading article...\" />;\n  }\n\n  if (!article) {\n    return (\n      <div className=\"page-container\">\n        <div className=\"empty-state\">\n          <div className=\"empty-state-icon\">📰</div>\n          <h3 className=\"empty-state-title\">Article Not Found</h3>\n          <p className=\"empty-state-text\">\n            The article you're looking for doesn't exist or has been removed.\n          </p>\n          <Link to=\"/news\" className=\"btn btn-primary mt-4\">\n            Back to News\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"page-container\">\n      {/* Back Button */}\n      <div className=\"mb-4\">\n        <Link to=\"/news\" className=\"btn btn-outline btn-sm\">\n          <FiArrowLeft className=\"mr-2\" />\n          Back to News\n        </Link>\n      </div>\n\n      {/* Article Content */}\n      <article className=\"article-container\">\n        {/* Article Header */}\n        <header className=\"article-header\">\n          <div className=\"article-meta mb-4\">\n            <div className=\"flex items-center gap-2 mb-2\">\n              <span className={`news-category ${getCategoryColor(article.category)}`}>\n                {article.category}\n              </span>\n              {article.priority !== 'medium' && (\n                <span className=\"priority-indicator\">\n                  {getPriorityIcon(article.priority)}\n                </span>\n              )}\n            </div>\n            <div className=\"flex items-center gap-4 text-sm text-gray-500\">\n              <div className=\"flex items-center gap-1\">\n                <FiCalendar size={14} />\n                <span>{formatDate(article.publishDate)}</span>\n              </div>\n              <div className=\"flex items-center gap-1\">\n                <FiEye size={14} />\n                <span>{article.views} views</span>\n              </div>\n              {article.author && (\n                <div className=\"flex items-center gap-1\">\n                  <FiUser size={14} />\n                  <span>by {article.author.name}</span>\n                </div>\n              )}\n            </div>\n          </div>\n\n          <h1 className=\"article-title\">{article.title}</h1>\n\n          {article.summary && (\n            <p className=\"article-summary\">{article.summary}</p>\n          )}\n        </header>\n\n        {/* Article Image */}\n        {article.image && (\n          <div className=\"article-image-container\">\n            <img\n              src={article.image}\n              alt={article.title}\n              className=\"article-image\"\n            />\n          </div>\n        )}\n\n        {/* Article Body */}\n        <div className=\"article-body\">\n          <div className=\"article-content\">\n            {article.content.split('\\n').map((paragraph, index) => (\n              <p key={index} className=\"mb-4\">\n                {paragraph}\n              </p>\n            ))}\n          </div>\n\n          {/* Tags */}\n          {article.tags && article.tags.length > 0 && (\n            <div className=\"article-tags\">\n              <h4 className=\"text-sm font-medium text-gray-700 mb-2\">Tags:</h4>\n              <div className=\"flex flex-wrap gap-2\">\n                {article.tags.map((tag, index) => (\n                  <span key={index} className=\"tag\">\n                    #{tag}\n                  </span>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Like Button */}\n          {user && (\n            <div className=\"article-actions\">\n              <button\n                onClick={handleLike}\n                disabled={liking}\n                className={`like-button ${article.isLiked ? 'liked' : ''}`}\n              >\n                <FiHeart className={article.isLiked ? 'fill-current' : ''} />\n                <span>{article.likeCount} {article.likeCount === 1 ? 'like' : 'likes'}</span>\n              </button>\n            </div>\n          )}\n        </div>\n      </article>\n\n      <style jsx>{`\n        .article-container {\n          max-width: 800px;\n          margin: 0 auto;\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          overflow: hidden;\n        }\n\n        .article-header {\n          padding: 2rem 2rem 1rem;\n        }\n\n        .news-category {\n          padding: 0.25rem 0.75rem;\n          border-radius: 9999px;\n          font-size: 0.75rem;\n          font-weight: 500;\n          text-transform: capitalize;\n        }\n\n        .priority-indicator {\n          font-size: 0.875rem;\n        }\n\n        .article-title {\n          font-size: 2.5rem;\n          font-weight: bold;\n          line-height: 1.2;\n          color: #1f2937;\n          margin-bottom: 1rem;\n        }\n\n        .article-summary {\n          font-size: 1.25rem;\n          color: #6b7280;\n          line-height: 1.6;\n          font-style: italic;\n        }\n\n        .article-image-container {\n          width: 100%;\n          max-height: 400px;\n          overflow: hidden;\n        }\n\n        .article-image {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n        }\n\n        .article-body {\n          padding: 2rem;\n        }\n\n        .article-content {\n          font-size: 1.125rem;\n          line-height: 1.8;\n          color: #374151;\n          margin-bottom: 2rem;\n        }\n\n        .article-tags {\n          margin-bottom: 2rem;\n          padding-top: 2rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .tag {\n          background: #f3f4f6;\n          color: #6b7280;\n          padding: 0.25rem 0.75rem;\n          border-radius: 9999px;\n          font-size: 0.875rem;\n        }\n\n        .article-actions {\n          padding-top: 2rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .like-button {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          background: none;\n          border: 2px solid #e5e7eb;\n          border-radius: 9999px;\n          padding: 0.75rem 1.5rem;\n          color: #6b7280;\n          cursor: pointer;\n          transition: all 0.2s ease;\n        }\n\n        .like-button:hover {\n          border-color: #ef4444;\n          color: #ef4444;\n        }\n\n        .like-button.liked {\n          border-color: #ef4444;\n          color: #ef4444;\n          background: #fef2f2;\n        }\n\n        .like-button:disabled {\n          opacity: 0.6;\n          cursor: not-allowed;\n        }\n\n        @media (max-width: 768px) {\n          .article-header {\n            padding: 1.5rem 1.5rem 1rem;\n          }\n\n          .article-body {\n            padding: 1.5rem;\n          }\n\n          .article-title {\n            font-size: 2rem;\n          }\n\n          .article-summary {\n            font-size: 1.125rem;\n          }\n\n          .article-content {\n            font-size: 1rem;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default NewsDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,UAAU,EAAEC,KAAK,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AAChF,OAAOC,cAAc,MAAM,8BAA8B;AACzD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAG,CAAC,GAAGf,SAAS,CAAC,CAAC;EAC1B,MAAMgB,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAE3CC,SAAS,CAAC,MAAM;IACdyB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACT,EAAE,CAAC,CAAC;EAER,MAAMS,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMf,KAAK,CAACgB,GAAG,CAAC,aAAaX,EAAE,EAAE,CAAC;MACnDI,UAAU,CAACM,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;IAChC,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAC,eAAA;MACdC,OAAO,CAACF,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,IAAI,EAAAC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,EAAE;QAClCf,QAAQ,CAAC,OAAO,CAAC;MACnB;IACF,CAAC,SAAS;MACRK,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACf,IAAI,EAAE;IAEXM,SAAS,CAAC,IAAI,CAAC;IACf,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMf,KAAK,CAACuB,IAAI,CAAC,kBAAkBlB,EAAE,EAAE,CAAC;MACzDI,UAAU,CAACe,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPC,OAAO,EAAEV,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACQ,OAAO;QACnCC,SAAS,EAAEX,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACS;MAChC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRL,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMc,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,MAAMC,MAAM,GAAG;MACbC,YAAY,EAAE,2BAA2B;MACzCC,KAAK,EAAE,6BAA6B;MACpCC,QAAQ,EAAE,yBAAyB;MACnCC,OAAO,EAAE;IACX,CAAC;IACD,OAAOJ,MAAM,CAACD,QAAQ,CAAC,IAAIC,MAAM,CAACI,OAAO;EAC3C,CAAC;EAED,MAAMC,eAAe,GAAIC,QAAQ,IAAK;IACpC,QAAQA,QAAQ;MACd,KAAK,QAAQ;QACX,OAAO,IAAI;MACb,KAAK,MAAM;QACT,OAAO,IAAI;MACb,KAAK,QAAQ;QACX,OAAO,IAAI;MACb,KAAK,KAAK;QACR,OAAO,IAAI;MACb;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,IAAIlC,OAAO,EAAE;IACX,oBAAOR,OAAA,CAACJ,cAAc;MAAC+C,IAAI,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACrD;EAEA,IAAI,CAACzC,OAAO,EAAE;IACZ,oBACEN,OAAA;MAAKgD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BjD,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjD,OAAA;UAAKgD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1C/C,OAAA;UAAIgD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAiB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxD/C,OAAA;UAAGgD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAEhC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ/C,OAAA,CAACX,IAAI;UAAC6D,EAAE,EAAC,OAAO;UAACF,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAElD;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/C,OAAA;IAAKgD,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAE7BjD,OAAA;MAAKgD,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBjD,OAAA,CAACX,IAAI;QAAC6D,EAAE,EAAC,OAAO;QAACF,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACjDjD,OAAA,CAACP,WAAW;UAACuD,SAAS,EAAC;QAAM;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAElC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN/C,OAAA;MAASgD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEpCjD,OAAA;QAAQgD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAChCjD,OAAA;UAAKgD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCjD,OAAA;YAAKgD,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBAC3CjD,OAAA;cAAMgD,SAAS,EAAE,iBAAiBd,gBAAgB,CAAC5B,OAAO,CAAC6B,QAAQ,CAAC,EAAG;cAAAc,QAAA,EACpE3C,OAAO,CAAC6B;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,EACNzC,OAAO,CAACoC,QAAQ,KAAK,QAAQ,iBAC5B1C,OAAA;cAAMgD,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EACjCR,eAAe,CAACnC,OAAO,CAACoC,QAAQ;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN/C,OAAA;YAAKgD,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5DjD,OAAA;cAAKgD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCjD,OAAA,CAACT,UAAU;gBAAC4D,IAAI,EAAE;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxB/C,OAAA;gBAAAiD,QAAA,EAAOxB,UAAU,CAACnB,OAAO,CAAC8C,WAAW;cAAC;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACN/C,OAAA;cAAKgD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCjD,OAAA,CAACR,KAAK;gBAAC2D,IAAI,EAAE;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnB/C,OAAA;gBAAAiD,QAAA,GAAO3C,OAAO,CAAC+C,KAAK,EAAC,QAAM;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,EACLzC,OAAO,CAACgD,MAAM,iBACbtD,OAAA;cAAKgD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCjD,OAAA,CAACL,MAAM;gBAACwD,IAAI,EAAE;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpB/C,OAAA;gBAAAiD,QAAA,GAAM,KAAG,EAAC3C,OAAO,CAACgD,MAAM,CAACC,IAAI;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA;UAAIgD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAE3C,OAAO,CAACkD;QAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAEjDzC,OAAO,CAACmD,OAAO,iBACdzD,OAAA;UAAGgD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAE3C,OAAO,CAACmD;QAAO;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CACpD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EAGRzC,OAAO,CAACoD,KAAK,iBACZ1D,OAAA;QAAKgD,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtCjD,OAAA;UACE2D,GAAG,EAAErD,OAAO,CAACoD,KAAM;UACnBE,GAAG,EAAEtD,OAAO,CAACkD,KAAM;UACnBR,SAAS,EAAC;QAAe;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAGD/C,OAAA;QAAKgD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BjD,OAAA;UAAKgD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7B3C,OAAO,CAACuD,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAACC,SAAS,EAAEC,KAAK,kBAChDjE,OAAA;YAAegD,SAAS,EAAC,MAAM;YAAAC,QAAA,EAC5Be;UAAS,GADJC,KAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGLzC,OAAO,CAAC4D,IAAI,IAAI5D,OAAO,CAAC4D,IAAI,CAACC,MAAM,GAAG,CAAC,iBACtCnE,OAAA;UAAKgD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BjD,OAAA;YAAIgD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjE/C,OAAA;YAAKgD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAClC3C,OAAO,CAAC4D,IAAI,CAACH,GAAG,CAAC,CAACK,GAAG,EAAEH,KAAK,kBAC3BjE,OAAA;cAAkBgD,SAAS,EAAC,KAAK;cAAAC,QAAA,GAAC,GAC/B,EAACmB,GAAG;YAAA,GADIH,KAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA1C,IAAI,iBACHL,OAAA;UAAKgD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BjD,OAAA;YACEqE,OAAO,EAAEjD,UAAW;YACpBkD,QAAQ,EAAE5D,MAAO;YACjBsC,SAAS,EAAE,eAAe1C,OAAO,CAACiB,OAAO,GAAG,OAAO,GAAG,EAAE,EAAG;YAAA0B,QAAA,gBAE3DjD,OAAA,CAACN,OAAO;cAACsD,SAAS,EAAE1C,OAAO,CAACiB,OAAO,GAAG,cAAc,GAAG;YAAG;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7D/C,OAAA;cAAAiD,QAAA,GAAO3C,OAAO,CAACkB,SAAS,EAAC,GAAC,EAAClB,OAAO,CAACkB,SAAS,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEV/C,OAAA;MAAOuE,GAAG;MAAAtB,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC7C,EAAA,CAjVID,UAAU;EAAA,QACCb,SAAS,EACPE,WAAW,EACXO,OAAO;AAAA;AAAA2E,EAAA,GAHpBvE,UAAU;AAmVhB,eAAeA,UAAU;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}