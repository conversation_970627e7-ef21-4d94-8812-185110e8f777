{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\OutSource\\\\CV\\\\frontend\\\\src\\\\pages\\\\News.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { FiCalendar, FiEye, FiSearch, FiFilter } from 'react-icons/fi';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst News = () => {\n  _s();\n  const [news, setNews] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  useEffect(() => {\n    fetchNews();\n  }, [currentPage, searchTerm, selectedCategory]);\n  const fetchNews = async () => {\n    setLoading(true);\n    try {\n      const params = {\n        page: currentPage,\n        limit: 6,\n        ...(searchTerm && {\n          search: searchTerm\n        }),\n        ...(selectedCategory && {\n          category: selectedCategory\n        })\n      };\n      const response = await axios.get('/api/news', {\n        params\n      });\n      setNews(response.data.data);\n      setTotalPages(response.data.pages);\n    } catch (error) {\n      console.error('Error fetching news:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSearch = e => {\n    e.preventDefault();\n    setCurrentPage(1);\n    fetchNews();\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n  const getCategoryColor = category => {\n    const colors = {\n      announcement: 'bg-blue-100 text-blue-800',\n      event: 'bg-green-100 text-green-800',\n      deadline: 'bg-red-100 text-red-800',\n      general: 'bg-gray-100 text-gray-800'\n    };\n    return colors[category] || colors.general;\n  };\n  const getPriorityIcon = priority => {\n    switch (priority) {\n      case 'urgent':\n        return '🔴';\n      case 'high':\n        return '🟠';\n      case 'medium':\n        return '🟡';\n      case 'low':\n        return '🟢';\n      default:\n        return '';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"page-title\",\n        children: \"News & Announcements\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"page-subtitle\",\n        children: \"Stay updated with the latest news and announcements\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSearch,\n          className: \"flex flex-col md:flex-row gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(FiSearch, {\n                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search news...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"form-input pl-10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-48\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedCategory,\n              onChange: e => {\n                setSelectedCategory(e.target.value);\n                setCurrentPage(1);\n              },\n              className: \"form-input\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"announcement\",\n                children: \"Announcements\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"event\",\n                children: \"Events\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"deadline\",\n                children: \"Deadlines\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"general\",\n                children: \"General\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n              className: \"mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), \"Filter\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      text: \"Loading news...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 9\n    }, this) : news.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-state\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state-icon\",\n        children: \"\\uD83D\\uDCF0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"empty-state-title\",\n        children: \"No News Found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"empty-state-text\",\n        children: searchTerm || selectedCategory ? 'No news articles match your search criteria.' : 'There are no news articles available at the moment.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"news-grid\",\n        children: news.map(article => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"news-card\",\n          children: [article.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"news-image-container\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: article.image,\n              alt: article.title,\n              className: \"news-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"news-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"news-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `news-category ${getCategoryColor(article.category)}`,\n                  children: article.category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 23\n                }, this), article.priority !== 'medium' && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"priority-indicator\",\n                  children: getPriorityIcon(article.priority)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"news-title\",\n                children: /*#__PURE__*/_jsxDEV(Link, {\n                  to: `/news/${article.id}`,\n                  children: article.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"news-summary\",\n              children: article.summary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"news-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-4 text-sm text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-1\",\n                  children: [/*#__PURE__*/_jsxDEV(FiCalendar, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: formatDate(article.publishDate)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-1\",\n                  children: [/*#__PURE__*/_jsxDEV(FiEye, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [article.views, \" views\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: `/news/${article.id}`,\n                className: \"text-blue-600 hover:text-blue-800 font-medium text-sm\",\n                children: \"Read more \\u2192\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 17\n          }, this)]\n        }, article.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(prev => Math.max(prev - 1, 1)),\n          disabled: currentPage === 1,\n          className: \"btn btn-outline\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-info\",\n          children: [\"Page \", currentPage, \" of \", totalPages]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentPage(prev => Math.min(prev + 1, totalPages)),\n          disabled: currentPage === totalPages,\n          className: \"btn btn-outline\",\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .news-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n          gap: 2rem;\n          margin-bottom: 2rem;\n        }\n\n        .news-card {\n          background: white;\n          border-radius: 0.75rem;\n          overflow: hidden;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          transition: all 0.2s ease;\n          border: 1px solid #e5e7eb;\n        }\n\n        .news-card:hover {\n          transform: translateY(-4px);\n          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n        }\n\n        .news-image-container {\n          width: 100%;\n          height: 200px;\n          overflow: hidden;\n        }\n\n        .news-image {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n          transition: transform 0.3s ease;\n        }\n\n        .news-card:hover .news-image {\n          transform: scale(1.05);\n        }\n\n        .news-content {\n          padding: 1.5rem;\n        }\n\n        .news-category {\n          padding: 0.25rem 0.75rem;\n          border-radius: 9999px;\n          font-size: 0.75rem;\n          font-weight: 500;\n          text-transform: capitalize;\n        }\n\n        .priority-indicator {\n          font-size: 0.875rem;\n        }\n\n        .news-title {\n          font-size: 1.25rem;\n          font-weight: 600;\n          line-height: 1.4;\n          margin-bottom: 0.75rem;\n        }\n\n        .news-title a {\n          color: #1f2937;\n          text-decoration: none;\n          transition: color 0.2s ease;\n        }\n\n        .news-title a:hover {\n          color: #3b82f6;\n        }\n\n        .news-summary {\n          color: #6b7280;\n          line-height: 1.6;\n          margin-bottom: 1rem;\n          display: -webkit-box;\n          -webkit-line-clamp: 3;\n          -webkit-box-orient: vertical;\n          overflow: hidden;\n        }\n\n        .news-meta {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          padding-top: 1rem;\n          border-top: 1px solid #f3f4f6;\n        }\n\n        .pagination {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 1rem;\n          margin-top: 2rem;\n        }\n\n        .pagination-info {\n          color: #6b7280;\n          font-size: 0.875rem;\n        }\n\n        @media (max-width: 768px) {\n          .news-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .news-meta {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 0.5rem;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n_s(News, \"LRinT7gvEDKyda9iWbigVptbcNg=\");\n_c = News;\nexport default News;\nvar _c;\n$RefreshReg$(_c, \"News\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "FiCalendar", "FiEye", "FiSearch", "<PERSON><PERSON><PERSON><PERSON>", "LoadingSpinner", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "News", "_s", "news", "setNews", "loading", "setLoading", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "fetchNews", "params", "page", "limit", "search", "category", "response", "get", "data", "pages", "error", "console", "handleSearch", "e", "preventDefault", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "getCategoryColor", "colors", "announcement", "event", "deadline", "general", "getPriorityIcon", "priority", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "placeholder", "value", "onChange", "target", "text", "length", "map", "article", "image", "src", "alt", "title", "to", "id", "summary", "size", "publishDate", "views", "onClick", "prev", "Math", "max", "disabled", "min", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/OutSource/CV/frontend/src/pages/News.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { FiCalendar, <PERSON>Eye, FiSearch, FiFilter } from 'react-icons/fi';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport axios from 'axios';\n\nconst News = () => {\n  const [news, setNews] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n\n  useEffect(() => {\n    fetchNews();\n  }, [currentPage, searchTerm, selectedCategory]);\n\n  const fetchNews = async () => {\n    setLoading(true);\n    try {\n      const params = {\n        page: currentPage,\n        limit: 6,\n        ...(searchTerm && { search: searchTerm }),\n        ...(selectedCategory && { category: selectedCategory })\n      };\n\n      const response = await axios.get('/api/news', { params });\n      setNews(response.data.data);\n      setTotalPages(response.data.pages);\n    } catch (error) {\n      console.error('Error fetching news:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    setCurrentPage(1);\n    fetchNews();\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  const getCategoryColor = (category) => {\n    const colors = {\n      announcement: 'bg-blue-100 text-blue-800',\n      event: 'bg-green-100 text-green-800',\n      deadline: 'bg-red-100 text-red-800',\n      general: 'bg-gray-100 text-gray-800'\n    };\n    return colors[category] || colors.general;\n  };\n\n  const getPriorityIcon = (priority) => {\n    switch (priority) {\n      case 'urgent':\n        return '🔴';\n      case 'high':\n        return '🟠';\n      case 'medium':\n        return '🟡';\n      case 'low':\n        return '🟢';\n      default:\n        return '';\n    }\n  };\n\n  return (\n    <div className=\"page-container\">\n      <div className=\"page-header\">\n        <h1 className=\"page-title\">News & Announcements</h1>\n        <p className=\"page-subtitle\">\n          Stay updated with the latest news and announcements\n        </p>\n      </div>\n\n      {/* Search and Filter */}\n      <div className=\"card mb-6\">\n        <div className=\"card-body\">\n          <form onSubmit={handleSearch} className=\"flex flex-col md:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <FiSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search news...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"form-input pl-10\"\n                />\n              </div>\n            </div>\n            <div className=\"md:w-48\">\n              <select\n                value={selectedCategory}\n                onChange={(e) => {\n                  setSelectedCategory(e.target.value);\n                  setCurrentPage(1);\n                }}\n                className=\"form-input\"\n              >\n                <option value=\"\">All Categories</option>\n                <option value=\"announcement\">Announcements</option>\n                <option value=\"event\">Events</option>\n                <option value=\"deadline\">Deadlines</option>\n                <option value=\"general\">General</option>\n              </select>\n            </div>\n            <button type=\"submit\" className=\"btn btn-primary\">\n              <FiFilter className=\"mr-2\" />\n              Filter\n            </button>\n          </form>\n        </div>\n      </div>\n\n      {/* News Grid */}\n      {loading ? (\n        <LoadingSpinner text=\"Loading news...\" />\n      ) : news.length === 0 ? (\n        <div className=\"empty-state\">\n          <div className=\"empty-state-icon\">📰</div>\n          <h3 className=\"empty-state-title\">No News Found</h3>\n          <p className=\"empty-state-text\">\n            {searchTerm || selectedCategory\n              ? 'No news articles match your search criteria.'\n              : 'There are no news articles available at the moment.'}\n          </p>\n        </div>\n      ) : (\n        <>\n          <div className=\"news-grid\">\n            {news.map((article) => (\n              <div key={article.id} className=\"news-card\">\n                {article.image && (\n                  <div className=\"news-image-container\">\n                    <img\n                      src={article.image}\n                      alt={article.title}\n                      className=\"news-image\"\n                    />\n                  </div>\n                )}\n                <div className=\"news-content\">\n                  <div className=\"news-header\">\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      <span className={`news-category ${getCategoryColor(article.category)}`}>\n                        {article.category}\n                      </span>\n                      {article.priority !== 'medium' && (\n                        <span className=\"priority-indicator\">\n                          {getPriorityIcon(article.priority)}\n                        </span>\n                      )}\n                    </div>\n                    <h3 className=\"news-title\">\n                      <Link to={`/news/${article.id}`}>\n                        {article.title}\n                      </Link>\n                    </h3>\n                  </div>\n                  \n                  <p className=\"news-summary\">\n                    {article.summary}\n                  </p>\n                  \n                  <div className=\"news-meta\">\n                    <div className=\"flex items-center gap-4 text-sm text-gray-500\">\n                      <div className=\"flex items-center gap-1\">\n                        <FiCalendar size={14} />\n                        <span>{formatDate(article.publishDate)}</span>\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <FiEye size={14} />\n                        <span>{article.views} views</span>\n                      </div>\n                    </div>\n                    <Link\n                      to={`/news/${article.id}`}\n                      className=\"text-blue-600 hover:text-blue-800 font-medium text-sm\"\n                    >\n                      Read more →\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div className=\"pagination\">\n              <button\n                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}\n                disabled={currentPage === 1}\n                className=\"btn btn-outline\"\n              >\n                Previous\n              </button>\n              \n              <div className=\"pagination-info\">\n                Page {currentPage} of {totalPages}\n              </div>\n              \n              <button\n                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}\n                disabled={currentPage === totalPages}\n                className=\"btn btn-outline\"\n              >\n                Next\n              </button>\n            </div>\n          )}\n        </>\n      )}\n\n      <style jsx>{`\n        .news-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n          gap: 2rem;\n          margin-bottom: 2rem;\n        }\n\n        .news-card {\n          background: white;\n          border-radius: 0.75rem;\n          overflow: hidden;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          transition: all 0.2s ease;\n          border: 1px solid #e5e7eb;\n        }\n\n        .news-card:hover {\n          transform: translateY(-4px);\n          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n        }\n\n        .news-image-container {\n          width: 100%;\n          height: 200px;\n          overflow: hidden;\n        }\n\n        .news-image {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n          transition: transform 0.3s ease;\n        }\n\n        .news-card:hover .news-image {\n          transform: scale(1.05);\n        }\n\n        .news-content {\n          padding: 1.5rem;\n        }\n\n        .news-category {\n          padding: 0.25rem 0.75rem;\n          border-radius: 9999px;\n          font-size: 0.75rem;\n          font-weight: 500;\n          text-transform: capitalize;\n        }\n\n        .priority-indicator {\n          font-size: 0.875rem;\n        }\n\n        .news-title {\n          font-size: 1.25rem;\n          font-weight: 600;\n          line-height: 1.4;\n          margin-bottom: 0.75rem;\n        }\n\n        .news-title a {\n          color: #1f2937;\n          text-decoration: none;\n          transition: color 0.2s ease;\n        }\n\n        .news-title a:hover {\n          color: #3b82f6;\n        }\n\n        .news-summary {\n          color: #6b7280;\n          line-height: 1.6;\n          margin-bottom: 1rem;\n          display: -webkit-box;\n          -webkit-line-clamp: 3;\n          -webkit-box-orient: vertical;\n          overflow: hidden;\n        }\n\n        .news-meta {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          padding-top: 1rem;\n          border-top: 1px solid #f3f4f6;\n        }\n\n        .pagination {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 1rem;\n          margin-top: 2rem;\n        }\n\n        .pagination-info {\n          color: #6b7280;\n          font-size: 0.875rem;\n        }\n\n        @media (max-width: 768px) {\n          .news-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .news-meta {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 0.5rem;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default News;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,UAAU,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AACtE,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EAE/CC,SAAS,CAAC,MAAM;IACd0B,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACJ,WAAW,EAAEJ,UAAU,EAAEE,gBAAgB,CAAC,CAAC;EAE/C,MAAMM,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BT,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMU,MAAM,GAAG;QACbC,IAAI,EAAEN,WAAW;QACjBO,KAAK,EAAE,CAAC;QACR,IAAIX,UAAU,IAAI;UAAEY,MAAM,EAAEZ;QAAW,CAAC,CAAC;QACzC,IAAIE,gBAAgB,IAAI;UAAEW,QAAQ,EAAEX;QAAiB,CAAC;MACxD,CAAC;MAED,MAAMY,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,WAAW,EAAE;QAAEN;MAAO,CAAC,CAAC;MACzDZ,OAAO,CAACiB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MAC3BT,aAAa,CAACO,QAAQ,CAACE,IAAI,CAACC,KAAK,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBjB,cAAc,CAAC,CAAC,CAAC;IACjBG,SAAS,CAAC,CAAC;EACb,CAAC;EAED,MAAMe,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAIjB,QAAQ,IAAK;IACrC,MAAMkB,MAAM,GAAG;MACbC,YAAY,EAAE,2BAA2B;MACzCC,KAAK,EAAE,6BAA6B;MACpCC,QAAQ,EAAE,yBAAyB;MACnCC,OAAO,EAAE;IACX,CAAC;IACD,OAAOJ,MAAM,CAAClB,QAAQ,CAAC,IAAIkB,MAAM,CAACI,OAAO;EAC3C,CAAC;EAED,MAAMC,eAAe,GAAIC,QAAQ,IAAK;IACpC,QAAQA,QAAQ;MACd,KAAK,QAAQ;QACX,OAAO,IAAI;MACb,KAAK,MAAM;QACT,OAAO,IAAI;MACb,KAAK,QAAQ;QACX,OAAO,IAAI;MACb,KAAK,KAAK;QACR,OAAO,IAAI;MACb;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,oBACE9C,OAAA;IAAK+C,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BhD,OAAA;MAAK+C,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BhD,OAAA;QAAI+C,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpDpD,OAAA;QAAG+C,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAE7B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNpD,OAAA;MAAK+C,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBhD,OAAA;QAAK+C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBhD,OAAA;UAAMqD,QAAQ,EAAExB,YAAa;UAACkB,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBACvEhD,OAAA;YAAK+C,SAAS,EAAC,QAAQ;YAAAC,QAAA,eACrBhD,OAAA;cAAK+C,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBhD,OAAA,CAACL,QAAQ;gBAACoD,SAAS,EAAC;cAAkE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzFpD,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,gBAAgB;gBAC5BC,KAAK,EAAE/C,UAAW;gBAClBgD,QAAQ,EAAG3B,CAAC,IAAKpB,aAAa,CAACoB,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;gBAC/CT,SAAS,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNpD,OAAA;YAAK+C,SAAS,EAAC,SAAS;YAAAC,QAAA,eACtBhD,OAAA;cACEwD,KAAK,EAAE7C,gBAAiB;cACxB8C,QAAQ,EAAG3B,CAAC,IAAK;gBACflB,mBAAmB,CAACkB,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAC;gBACnC1C,cAAc,CAAC,CAAC,CAAC;cACnB,CAAE;cACFiC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAEtBhD,OAAA;gBAAQwD,KAAK,EAAC,EAAE;gBAAAR,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCpD,OAAA;gBAAQwD,KAAK,EAAC,cAAc;gBAAAR,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnDpD,OAAA;gBAAQwD,KAAK,EAAC,OAAO;gBAAAR,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrCpD,OAAA;gBAAQwD,KAAK,EAAC,UAAU;gBAAAR,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3CpD,OAAA;gBAAQwD,KAAK,EAAC,SAAS;gBAAAR,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNpD,OAAA;YAAQsD,IAAI,EAAC,QAAQ;YAACP,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC/ChD,OAAA,CAACJ,QAAQ;cAACmD,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAE/B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL7C,OAAO,gBACNP,OAAA,CAACH,cAAc;MAAC8D,IAAI,EAAC;IAAiB;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GACvC/C,IAAI,CAACuD,MAAM,KAAK,CAAC,gBACnB5D,OAAA;MAAK+C,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BhD,OAAA;QAAK+C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC1CpD,OAAA;QAAI+C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpDpD,OAAA;QAAG+C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAC5BvC,UAAU,IAAIE,gBAAgB,GAC3B,8CAA8C,GAC9C;MAAqD;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,gBAENpD,OAAA,CAAAE,SAAA;MAAA8C,QAAA,gBACEhD,OAAA;QAAK+C,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB3C,IAAI,CAACwD,GAAG,CAAEC,OAAO,iBAChB9D,OAAA;UAAsB+C,SAAS,EAAC,WAAW;UAAAC,QAAA,GACxCc,OAAO,CAACC,KAAK,iBACZ/D,OAAA;YAAK+C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnChD,OAAA;cACEgE,GAAG,EAAEF,OAAO,CAACC,KAAM;cACnBE,GAAG,EAAEH,OAAO,CAACI,KAAM;cACnBnB,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eACDpD,OAAA;YAAK+C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BhD,OAAA;cAAK+C,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BhD,OAAA;gBAAK+C,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3ChD,OAAA;kBAAM+C,SAAS,EAAE,iBAAiBR,gBAAgB,CAACuB,OAAO,CAACxC,QAAQ,CAAC,EAAG;kBAAA0B,QAAA,EACpEc,OAAO,CAACxC;gBAAQ;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,EACNU,OAAO,CAAChB,QAAQ,KAAK,QAAQ,iBAC5B9C,OAAA;kBAAM+C,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EACjCH,eAAe,CAACiB,OAAO,CAAChB,QAAQ;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNpD,OAAA;gBAAI+C,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACxBhD,OAAA,CAACR,IAAI;kBAAC2E,EAAE,EAAE,SAASL,OAAO,CAACM,EAAE,EAAG;kBAAApB,QAAA,EAC7Bc,OAAO,CAACI;gBAAK;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENpD,OAAA;cAAG+C,SAAS,EAAC,cAAc;cAAAC,QAAA,EACxBc,OAAO,CAACO;YAAO;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEJpD,OAAA;cAAK+C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBhD,OAAA;gBAAK+C,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,gBAC5DhD,OAAA;kBAAK+C,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtChD,OAAA,CAACP,UAAU;oBAAC6E,IAAI,EAAE;kBAAG;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxBpD,OAAA;oBAAAgD,QAAA,EAAOhB,UAAU,CAAC8B,OAAO,CAACS,WAAW;kBAAC;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNpD,OAAA;kBAAK+C,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtChD,OAAA,CAACN,KAAK;oBAAC4E,IAAI,EAAE;kBAAG;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnBpD,OAAA;oBAAAgD,QAAA,GAAOc,OAAO,CAACU,KAAK,EAAC,QAAM;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpD,OAAA,CAACR,IAAI;gBACH2E,EAAE,EAAE,SAASL,OAAO,CAACM,EAAE,EAAG;gBAC1BrB,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAClE;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAnDEU,OAAO,CAACM,EAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoDf,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLrC,UAAU,GAAG,CAAC,iBACbf,OAAA;QAAK+C,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBhD,OAAA;UACEyE,OAAO,EAAEA,CAAA,KAAM3D,cAAc,CAAC4D,IAAI,IAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAE;UAC7DG,QAAQ,EAAEhE,WAAW,KAAK,CAAE;UAC5BkC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETpD,OAAA;UAAK+C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,OAC1B,EAACnC,WAAW,EAAC,MAAI,EAACE,UAAU;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eAENpD,OAAA;UACEyE,OAAO,EAAEA,CAAA,KAAM3D,cAAc,CAAC4D,IAAI,IAAIC,IAAI,CAACG,GAAG,CAACJ,IAAI,GAAG,CAAC,EAAE3D,UAAU,CAAC,CAAE;UACtE8D,QAAQ,EAAEhE,WAAW,KAAKE,UAAW;UACrCgC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA,eACD,CACH,eAEDpD,OAAA;MAAO+E,GAAG;MAAA/B,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAChD,EAAA,CAjVID,IAAI;AAAA6E,EAAA,GAAJ7E,IAAI;AAmVV,eAAeA,IAAI;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}