{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\OutSource\\\\CV\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport axios from 'axios';\nimport { toast } from 'react-toastify';\n\n// Initial state\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  loading: true,\n  error: null\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  UPDATE_USER: 'UPDATE_USER',\n  SET_LOADING: 'SET_LOADING'\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        loading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        loading: false,\n        error: action.payload\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        loading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n    case AUTH_ACTIONS.UPDATE_USER:\n      return {\n        ...state,\n        user: {\n          ...state.user,\n          ...action.payload\n        }\n      };\n    case AUTH_ACTIONS.SET_LOADING:\n      return {\n        ...state,\n        loading: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Auth provider component\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Set up axios interceptors\n  useEffect(() => {\n    // Request interceptor to add token\n    const requestInterceptor = axios.interceptors.request.use(config => {\n      if (state.token) {\n        config.headers.Authorization = `Bearer ${state.token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // Response interceptor to handle token expiration\n    const responseInterceptor = axios.interceptors.response.use(response => response, error => {\n      var _error$response;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && state.token) {\n        logout();\n        toast.error('Session expired. Please login again.');\n      }\n      return Promise.reject(error);\n    });\n    return () => {\n      axios.interceptors.request.eject(requestInterceptor);\n      axios.interceptors.response.eject(responseInterceptor);\n    };\n  }, [state.token]);\n\n  // Load user on app start\n  useEffect(() => {\n    const loadUser = async () => {\n      if (state.token) {\n        try {\n          const response = await axios.get('/api/auth/me');\n          dispatch({\n            type: AUTH_ACTIONS.LOGIN_SUCCESS,\n            payload: {\n              user: response.data.user,\n              token: state.token\n            }\n          });\n        } catch (error) {\n          localStorage.removeItem('token');\n          dispatch({\n            type: AUTH_ACTIONS.LOGOUT\n          });\n        }\n      } else {\n        dispatch({\n          type: AUTH_ACTIONS.SET_LOADING,\n          payload: false\n        });\n      }\n    };\n    loadUser();\n  }, []);\n\n  // Login function\n  const login = async (email, password) => {\n    try {\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_START\n      });\n      const response = await axios.post('/api/auth/login', {\n        email,\n        password\n      });\n      const {\n        token,\n        user\n      } = response.data;\n      localStorage.setItem('token', token);\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: {\n          user,\n          token\n        }\n      });\n      toast.success('Login successful!');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: message\n      });\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n\n  // Register function\n  const register = async userData => {\n    try {\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_START\n      });\n      const response = await axios.post('/api/auth/register', userData);\n      const {\n        token,\n        user\n      } = response.data;\n      localStorage.setItem('token', token);\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: {\n          user,\n          token\n        }\n      });\n      toast.success('Registration successful!');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const message = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: message\n      });\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await axios.post('/api/auth/logout');\n    } catch (error) {\n      // Continue with logout even if API call fails\n    }\n    localStorage.removeItem('token');\n    dispatch({\n      type: AUTH_ACTIONS.LOGOUT\n    });\n    toast.success('Logged out successfully');\n  };\n\n  // Update profile function\n  const updateProfile = async profileData => {\n    try {\n      const response = await axios.put('/api/auth/profile', profileData);\n      dispatch({\n        type: AUTH_ACTIONS.UPDATE_USER,\n        payload: response.data.user\n      });\n      toast.success('Profile updated successfully!');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      const message = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Profile update failed';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n\n  // Change password function\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      await axios.put('/api/auth/change-password', {\n        currentPassword,\n        newPassword\n      });\n      toast.success('Password changed successfully!');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      const message = ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'Password change failed';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({\n      type: AUTH_ACTIONS.CLEAR_ERROR\n    });\n  };\n  const value = {\n    user: state.user,\n    token: state.token,\n    loading: state.loading,\n    error: state.error,\n    login,\n    register,\n    logout,\n    updateProfile,\n    changePassword,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 280,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"GUSXxL/WUElrtHc/X73NyHNRMdw=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "axios", "toast", "jsxDEV", "_jsxDEV", "initialState", "user", "token", "localStorage", "getItem", "loading", "error", "AUTH_ACTIONS", "LOGIN_START", "LOGIN_SUCCESS", "LOGIN_FAILURE", "LOGOUT", "CLEAR_ERROR", "UPDATE_USER", "SET_LOADING", "authReducer", "state", "action", "type", "payload", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "dispatch", "requestInterceptor", "interceptors", "request", "use", "config", "headers", "Authorization", "Promise", "reject", "responseInterceptor", "response", "_error$response", "status", "logout", "eject", "loadUser", "get", "data", "removeItem", "login", "email", "password", "post", "setItem", "success", "_error$response2", "_error$response2$data", "message", "register", "userData", "_error$response3", "_error$response3$data", "updateProfile", "profileData", "put", "_error$response4", "_error$response4$data", "changePassword", "currentPassword", "newPassword", "_error$response5", "_error$response5$data", "clearError", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/OutSource/CV/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport axios from 'axios';\nimport { toast } from 'react-toastify';\n\n// Initial state\nconst initialState = {\n  user: null,\n  token: localStorage.getItem('token'),\n  loading: true,\n  error: null\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  LOGIN_START: 'LOGIN_START',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  UPDATE_USER: 'UPDATE_USER',\n  SET_LOADING: 'SET_LOADING'\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.LOGIN_START:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        loading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGIN_FAILURE:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        loading: false,\n        error: action.payload\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        loading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n    case AUTH_ACTIONS.UPDATE_USER:\n      return {\n        ...state,\n        user: { ...state.user, ...action.payload }\n      };\n    case AUTH_ACTIONS.SET_LOADING:\n      return {\n        ...state,\n        loading: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = createContext();\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Auth provider component\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Set up axios interceptors\n  useEffect(() => {\n    // Request interceptor to add token\n    const requestInterceptor = axios.interceptors.request.use(\n      (config) => {\n        if (state.token) {\n          config.headers.Authorization = `Bearer ${state.token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor to handle token expiration\n    const responseInterceptor = axios.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        if (error.response?.status === 401 && state.token) {\n          logout();\n          toast.error('Session expired. Please login again.');\n        }\n        return Promise.reject(error);\n      }\n    );\n\n    return () => {\n      axios.interceptors.request.eject(requestInterceptor);\n      axios.interceptors.response.eject(responseInterceptor);\n    };\n  }, [state.token]);\n\n  // Load user on app start\n  useEffect(() => {\n    const loadUser = async () => {\n      if (state.token) {\n        try {\n          const response = await axios.get('/api/auth/me');\n          dispatch({\n            type: AUTH_ACTIONS.LOGIN_SUCCESS,\n            payload: {\n              user: response.data.user,\n              token: state.token\n            }\n          });\n        } catch (error) {\n          localStorage.removeItem('token');\n          dispatch({ type: AUTH_ACTIONS.LOGOUT });\n        }\n      } else {\n        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });\n      }\n    };\n\n    loadUser();\n  }, []);\n\n  // Login function\n  const login = async (email, password) => {\n    try {\n      dispatch({ type: AUTH_ACTIONS.LOGIN_START });\n\n      const response = await axios.post('/api/auth/login', {\n        email,\n        password\n      });\n\n      const { token, user } = response.data;\n\n      localStorage.setItem('token', token);\n\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: { user, token }\n      });\n\n      toast.success('Login successful!');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: message\n      });\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  // Register function\n  const register = async (userData) => {\n    try {\n      dispatch({ type: AUTH_ACTIONS.LOGIN_START });\n\n      const response = await axios.post('/api/auth/register', userData);\n\n      const { token, user } = response.data;\n\n      localStorage.setItem('token', token);\n\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: { user, token }\n      });\n\n      toast.success('Registration successful!');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_FAILURE,\n        payload: message\n      });\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await axios.post('/api/auth/logout');\n    } catch (error) {\n      // Continue with logout even if API call fails\n    }\n\n    localStorage.removeItem('token');\n    dispatch({ type: AUTH_ACTIONS.LOGOUT });\n    toast.success('Logged out successfully');\n  };\n\n  // Update profile function\n  const updateProfile = async (profileData) => {\n    try {\n      const response = await axios.put('/api/auth/profile', profileData);\n      \n      dispatch({\n        type: AUTH_ACTIONS.UPDATE_USER,\n        payload: response.data.user\n      });\n\n      toast.success('Profile updated successfully!');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Profile update failed';\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  // Change password function\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      await axios.put('/api/auth/change-password', {\n        currentPassword,\n        newPassword\n      });\n\n      toast.success('Password changed successfully!');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Password change failed';\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n  };\n\n  const value = {\n    user: state.user,\n    token: state.token,\n    loading: state.loading,\n    error: state.error,\n    login,\n    register,\n    logout,\n    updateProfile,\n    changePassword,\n    clearError\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,gBAAgB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAEC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACpCC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACnBC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,aAAa,EAAE,eAAe;EAC9BC,MAAM,EAAE,QAAQ;EAChBC,WAAW,EAAE,aAAa;EAC1BC,WAAW,EAAE,aAAa;EAC1BC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKX,YAAY,CAACC,WAAW;MAC3B,OAAO;QACL,GAAGQ,KAAK;QACRX,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACE,aAAa;MAC7B,OAAO;QACL,GAAGO,KAAK;QACRf,IAAI,EAAEgB,MAAM,CAACE,OAAO,CAAClB,IAAI;QACzBC,KAAK,EAAEe,MAAM,CAACE,OAAO,CAACjB,KAAK;QAC3BG,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACG,aAAa;MAC7B,OAAO;QACL,GAAGM,KAAK;QACRf,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXG,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEW,MAAM,CAACE;MAChB,CAAC;IACH,KAAKZ,YAAY,CAACI,MAAM;MACtB,OAAO;QACL,GAAGK,KAAK;QACRf,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXG,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACK,WAAW;MAC3B,OAAO;QACL,GAAGI,KAAK;QACRV,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACM,WAAW;MAC3B,OAAO;QACL,GAAGG,KAAK;QACRf,IAAI,EAAE;UAAE,GAAGe,KAAK,CAACf,IAAI;UAAE,GAAGgB,MAAM,CAACE;QAAQ;MAC3C,CAAC;IACH,KAAKZ,YAAY,CAACO,WAAW;MAC3B,OAAO;QACL,GAAGE,KAAK;QACRX,OAAO,EAAEY,MAAM,CAACE;MAClB,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,WAAW,gBAAG5B,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAM6B,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAG9B,UAAU,CAAC2B,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,EAAA,CARaD,OAAO;AASpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACX,KAAK,EAAEY,QAAQ,CAAC,GAAGlC,UAAU,CAACqB,WAAW,EAAEf,YAAY,CAAC;;EAE/D;EACAL,SAAS,CAAC,MAAM;IACd;IACA,MAAMkC,kBAAkB,GAAGjC,KAAK,CAACkC,YAAY,CAACC,OAAO,CAACC,GAAG,CACtDC,MAAM,IAAK;MACV,IAAIjB,KAAK,CAACd,KAAK,EAAE;QACf+B,MAAM,CAACC,OAAO,CAACC,aAAa,GAAG,UAAUnB,KAAK,CAACd,KAAK,EAAE;MACxD;MACA,OAAO+B,MAAM;IACf,CAAC,EACA3B,KAAK,IAAK;MACT,OAAO8B,OAAO,CAACC,MAAM,CAAC/B,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,MAAMgC,mBAAmB,GAAG1C,KAAK,CAACkC,YAAY,CAACS,QAAQ,CAACP,GAAG,CACxDO,QAAQ,IAAKA,QAAQ,EACrBjC,KAAK,IAAK;MAAA,IAAAkC,eAAA;MACT,IAAI,EAAAA,eAAA,GAAAlC,KAAK,CAACiC,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,IAAIzB,KAAK,CAACd,KAAK,EAAE;QACjDwC,MAAM,CAAC,CAAC;QACR7C,KAAK,CAACS,KAAK,CAAC,sCAAsC,CAAC;MACrD;MACA,OAAO8B,OAAO,CAACC,MAAM,CAAC/B,KAAK,CAAC;IAC9B,CACF,CAAC;IAED,OAAO,MAAM;MACXV,KAAK,CAACkC,YAAY,CAACC,OAAO,CAACY,KAAK,CAACd,kBAAkB,CAAC;MACpDjC,KAAK,CAACkC,YAAY,CAACS,QAAQ,CAACI,KAAK,CAACL,mBAAmB,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAACtB,KAAK,CAACd,KAAK,CAAC,CAAC;;EAEjB;EACAP,SAAS,CAAC,MAAM;IACd,MAAMiD,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI5B,KAAK,CAACd,KAAK,EAAE;QACf,IAAI;UACF,MAAMqC,QAAQ,GAAG,MAAM3C,KAAK,CAACiD,GAAG,CAAC,cAAc,CAAC;UAChDjB,QAAQ,CAAC;YACPV,IAAI,EAAEX,YAAY,CAACE,aAAa;YAChCU,OAAO,EAAE;cACPlB,IAAI,EAAEsC,QAAQ,CAACO,IAAI,CAAC7C,IAAI;cACxBC,KAAK,EAAEc,KAAK,CAACd;YACf;UACF,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;UACdH,YAAY,CAAC4C,UAAU,CAAC,OAAO,CAAC;UAChCnB,QAAQ,CAAC;YAAEV,IAAI,EAAEX,YAAY,CAACI;UAAO,CAAC,CAAC;QACzC;MACF,CAAC,MAAM;QACLiB,QAAQ,CAAC;UAAEV,IAAI,EAAEX,YAAY,CAACO,WAAW;UAAEK,OAAO,EAAE;QAAM,CAAC,CAAC;MAC9D;IACF,CAAC;IAEDyB,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACFtB,QAAQ,CAAC;QAAEV,IAAI,EAAEX,YAAY,CAACC;MAAY,CAAC,CAAC;MAE5C,MAAM+B,QAAQ,GAAG,MAAM3C,KAAK,CAACuD,IAAI,CAAC,iBAAiB,EAAE;QACnDF,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,MAAM;QAAEhD,KAAK;QAAED;MAAK,CAAC,GAAGsC,QAAQ,CAACO,IAAI;MAErC3C,YAAY,CAACiD,OAAO,CAAC,OAAO,EAAElD,KAAK,CAAC;MAEpC0B,QAAQ,CAAC;QACPV,IAAI,EAAEX,YAAY,CAACE,aAAa;QAChCU,OAAO,EAAE;UAAElB,IAAI;UAAEC;QAAM;MACzB,CAAC,CAAC;MAEFL,KAAK,CAACwD,OAAO,CAAC,mBAAmB,CAAC;MAClC,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAO/C,KAAK,EAAE;MAAA,IAAAgD,gBAAA,EAAAC,qBAAA;MACd,MAAMC,OAAO,GAAG,EAAAF,gBAAA,GAAAhD,KAAK,CAACiC,QAAQ,cAAAe,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBR,IAAI,cAAAS,qBAAA,uBAApBA,qBAAA,CAAsBC,OAAO,KAAI,cAAc;MAC/D5B,QAAQ,CAAC;QACPV,IAAI,EAAEX,YAAY,CAACG,aAAa;QAChCS,OAAO,EAAEqC;MACX,CAAC,CAAC;MACF3D,KAAK,CAACS,KAAK,CAACkD,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAE/C,KAAK,EAAEkD;MAAQ,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACF9B,QAAQ,CAAC;QAAEV,IAAI,EAAEX,YAAY,CAACC;MAAY,CAAC,CAAC;MAE5C,MAAM+B,QAAQ,GAAG,MAAM3C,KAAK,CAACuD,IAAI,CAAC,oBAAoB,EAAEO,QAAQ,CAAC;MAEjE,MAAM;QAAExD,KAAK;QAAED;MAAK,CAAC,GAAGsC,QAAQ,CAACO,IAAI;MAErC3C,YAAY,CAACiD,OAAO,CAAC,OAAO,EAAElD,KAAK,CAAC;MAEpC0B,QAAQ,CAAC;QACPV,IAAI,EAAEX,YAAY,CAACE,aAAa;QAChCU,OAAO,EAAE;UAAElB,IAAI;UAAEC;QAAM;MACzB,CAAC,CAAC;MAEFL,KAAK,CAACwD,OAAO,CAAC,0BAA0B,CAAC;MACzC,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAO/C,KAAK,EAAE;MAAA,IAAAqD,gBAAA,EAAAC,qBAAA;MACd,MAAMJ,OAAO,GAAG,EAAAG,gBAAA,GAAArD,KAAK,CAACiC,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBb,IAAI,cAAAc,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI,qBAAqB;MACtE5B,QAAQ,CAAC;QACPV,IAAI,EAAEX,YAAY,CAACG,aAAa;QAChCS,OAAO,EAAEqC;MACX,CAAC,CAAC;MACF3D,KAAK,CAACS,KAAK,CAACkD,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAE/C,KAAK,EAAEkD;MAAQ,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMd,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAM9C,KAAK,CAACuD,IAAI,CAAC,kBAAkB,CAAC;IACtC,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACd;IAAA;IAGFH,YAAY,CAAC4C,UAAU,CAAC,OAAO,CAAC;IAChCnB,QAAQ,CAAC;MAAEV,IAAI,EAAEX,YAAY,CAACI;IAAO,CAAC,CAAC;IACvCd,KAAK,CAACwD,OAAO,CAAC,yBAAyB,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAG,MAAOC,WAAW,IAAK;IAC3C,IAAI;MACF,MAAMvB,QAAQ,GAAG,MAAM3C,KAAK,CAACmE,GAAG,CAAC,mBAAmB,EAAED,WAAW,CAAC;MAElElC,QAAQ,CAAC;QACPV,IAAI,EAAEX,YAAY,CAACM,WAAW;QAC9BM,OAAO,EAAEoB,QAAQ,CAACO,IAAI,CAAC7C;MACzB,CAAC,CAAC;MAEFJ,KAAK,CAACwD,OAAO,CAAC,+BAA+B,CAAC;MAC9C,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAO/C,KAAK,EAAE;MAAA,IAAA0D,gBAAA,EAAAC,qBAAA;MACd,MAAMT,OAAO,GAAG,EAAAQ,gBAAA,GAAA1D,KAAK,CAACiC,QAAQ,cAAAyB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlB,IAAI,cAAAmB,qBAAA,uBAApBA,qBAAA,CAAsBT,OAAO,KAAI,uBAAuB;MACxE3D,KAAK,CAACS,KAAK,CAACkD,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAE/C,KAAK,EAAEkD;MAAQ,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMU,cAAc,GAAG,MAAAA,CAAOC,eAAe,EAAEC,WAAW,KAAK;IAC7D,IAAI;MACF,MAAMxE,KAAK,CAACmE,GAAG,CAAC,2BAA2B,EAAE;QAC3CI,eAAe;QACfC;MACF,CAAC,CAAC;MAEFvE,KAAK,CAACwD,OAAO,CAAC,gCAAgC,CAAC;MAC/C,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAO/C,KAAK,EAAE;MAAA,IAAA+D,gBAAA,EAAAC,qBAAA;MACd,MAAMd,OAAO,GAAG,EAAAa,gBAAA,GAAA/D,KAAK,CAACiC,QAAQ,cAAA8B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvB,IAAI,cAAAwB,qBAAA,uBAApBA,qBAAA,CAAsBd,OAAO,KAAI,wBAAwB;MACzE3D,KAAK,CAACS,KAAK,CAACkD,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAE/C,KAAK,EAAEkD;MAAQ,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMe,UAAU,GAAGA,CAAA,KAAM;IACvB3C,QAAQ,CAAC;MAAEV,IAAI,EAAEX,YAAY,CAACK;IAAY,CAAC,CAAC;EAC9C,CAAC;EAED,MAAM4D,KAAK,GAAG;IACZvE,IAAI,EAAEe,KAAK,CAACf,IAAI;IAChBC,KAAK,EAAEc,KAAK,CAACd,KAAK;IAClBG,OAAO,EAAEW,KAAK,CAACX,OAAO;IACtBC,KAAK,EAAEU,KAAK,CAACV,KAAK;IAClB0C,KAAK;IACLS,QAAQ;IACRf,MAAM;IACNmB,aAAa;IACbK,cAAc;IACdK;EACF,CAAC;EAED,oBACExE,OAAA,CAACqB,WAAW,CAACqD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA9C,QAAA,EAChCA;EAAQ;IAAAgD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAClD,GAAA,CAlMWF,YAAY;AAAAqD,EAAA,GAAZrD,YAAY;AAAA,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}