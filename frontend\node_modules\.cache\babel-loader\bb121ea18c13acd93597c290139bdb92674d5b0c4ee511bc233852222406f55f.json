{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\OutSource\\\\CV\\\\frontend\\\\src\\\\pages\\\\SignUp.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { FiUser, FiMail, FiLock, FiEye, FiEyeOff, FiPhone, FiBook, FiHash } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SignUp = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    studentId: '',\n    department: '',\n    year: '',\n    phone: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const {\n    register,\n    loading\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name) {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.length < 2) {\n      newErrors.name = 'Name must be at least 2 characters';\n    }\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    if (formData.year && (formData.year < 1 || formData.year > 4)) {\n      newErrors.year = 'Year must be between 1 and 4';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    const {\n      confirmPassword,\n      ...registrationData\n    } = formData;\n\n    // Convert year to number if provided\n    if (registrationData.year) {\n      registrationData.year = parseInt(registrationData.year);\n    }\n    const result = await register(registrationData);\n    if (result.success) {\n      navigate('/dashboard');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-image-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-image-overlay\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-image-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"auth-image-title\",\n              children: \"Join Our Community!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"auth-image-subtitle\",\n              children: \"Start your career journey with us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"auth-features\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-icon\",\n                  children: \"\\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Boost Your Career\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-icon\",\n                  children: \"\\uD83D\\uDCBC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Professional CV Builder\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-icon\",\n                  children: \"\\uD83C\\uDF93\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Skill Development\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-form-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-form-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"auth-title\",\n              children: \"Create Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"auth-subtitle\",\n              children: \"Join our CV management system\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"auth-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                className: \"form-label\",\n                children: \"Full Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-wrapper\",\n                children: [/*#__PURE__*/_jsxDEV(FiUser, {\n                  className: \"input-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"name\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  className: `form-input ${errors.name ? 'error' : ''}`,\n                  placeholder: \"Enter your full name\",\n                  disabled: loading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-error\",\n                children: errors.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"email\",\n                className: \"form-label\",\n                children: \"Email Address *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-wrapper\",\n                children: [/*#__PURE__*/_jsxDEV(FiMail, {\n                  className: \"input-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  id: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  className: `form-input ${errors.email ? 'error' : ''}`,\n                  placeholder: \"Enter your email\",\n                  disabled: loading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-error\",\n                children: errors.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"password\",\n                  className: \"form-label\",\n                  children: \"Password *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"input-wrapper\",\n                  children: [/*#__PURE__*/_jsxDEV(FiLock, {\n                    className: \"input-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: showPassword ? 'text' : 'password',\n                    id: \"password\",\n                    name: \"password\",\n                    value: formData.password,\n                    onChange: handleChange,\n                    className: `form-input ${errors.password ? 'error' : ''}`,\n                    placeholder: \"Enter password\",\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"password-toggle\",\n                    onClick: () => setShowPassword(!showPassword),\n                    children: showPassword ? /*#__PURE__*/_jsxDEV(FiEyeOff, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 39\n                    }, this) : /*#__PURE__*/_jsxDEV(FiEye, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 54\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"form-error\",\n                  children: errors.password\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"confirmPassword\",\n                  className: \"form-label\",\n                  children: \"Confirm Password *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"input-wrapper\",\n                  children: [/*#__PURE__*/_jsxDEV(FiLock, {\n                    className: \"input-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: showConfirmPassword ? 'text' : 'password',\n                    id: \"confirmPassword\",\n                    name: \"confirmPassword\",\n                    value: formData.confirmPassword,\n                    onChange: handleChange,\n                    className: `form-input ${errors.confirmPassword ? 'error' : ''}`,\n                    placeholder: \"Confirm password\",\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"password-toggle\",\n                    onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                    children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(FiEyeOff, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 46\n                    }, this) : /*#__PURE__*/_jsxDEV(FiEye, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 61\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 19\n                }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"form-error\",\n                  children: errors.confirmPassword\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"studentId\",\n                  className: \"form-label\",\n                  children: \"Student ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"input-wrapper\",\n                  children: [/*#__PURE__*/_jsxDEV(FiHash, {\n                    className: \"input-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    id: \"studentId\",\n                    name: \"studentId\",\n                    value: formData.studentId,\n                    onChange: handleChange,\n                    className: \"form-input\",\n                    placeholder: \"Enter student ID\",\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"year\",\n                  className: \"form-label\",\n                  children: \"Year\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"input-wrapper\",\n                  children: [/*#__PURE__*/_jsxDEV(FiBook, {\n                    className: \"input-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    id: \"year\",\n                    name: \"year\",\n                    value: formData.year,\n                    onChange: handleChange,\n                    className: `form-input ${errors.year ? 'error' : ''}`,\n                    disabled: loading,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"1\",\n                      children: \"1st Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"2\",\n                      children: \"2nd Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"3\",\n                      children: \"3rd Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"4\",\n                      children: \"4th Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 274,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 19\n                }, this), errors.year && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"form-error\",\n                  children: errors.year\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"department\",\n                className: \"form-label\",\n                children: \"Department\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-wrapper\",\n                children: [/*#__PURE__*/_jsxDEV(FiBook, {\n                  className: \"input-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"department\",\n                  name: \"department\",\n                  value: formData.department,\n                  onChange: handleChange,\n                  className: \"form-input\",\n                  placeholder: \"Enter your department\",\n                  disabled: loading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"phone\",\n                className: \"form-label\",\n                children: \"Phone Number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-wrapper\",\n                children: [/*#__PURE__*/_jsxDEV(FiPhone, {\n                  className: \"input-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  id: \"phone\",\n                  name: \"phone\",\n                  value: formData.phone,\n                  onChange: handleChange,\n                  className: \"form-input\",\n                  placeholder: \"Enter your phone number\",\n                  disabled: loading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-primary btn-full\",\n              disabled: loading,\n              children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"btn-loading\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"spinner-small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 21\n                }, this), \"Creating Account...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this) : 'Create Account'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-footer\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/signin\",\n                className: \"auth-link\",\n                children: \"Sign In\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(SignUp, \"7A0DpsHaHJ6OOJyecQVStesyTzc=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = SignUp;\nexport default SignUp;\nvar _c;\n$RefreshReg$(_c, \"SignUp\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "FiUser", "FiMail", "FiLock", "FiEye", "Fi<PERSON>ye<PERSON>ff", "FiPhone", "FiBook", "FiHash", "useAuth", "jsxDEV", "_jsxDEV", "SignUp", "_s", "formData", "setFormData", "name", "email", "password", "confirmPassword", "studentId", "department", "year", "phone", "errors", "setErrors", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "register", "loading", "navigate", "handleChange", "e", "value", "target", "prev", "validateForm", "newErrors", "length", "test", "Object", "keys", "handleSubmit", "preventDefault", "registrationData", "parseInt", "result", "success", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "disabled", "onClick", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/OutSource/CV/frontend/src/pages/SignUp.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { <PERSON><PERSON><PERSON>, FiMail, FiLock, FiEye, FiEyeOff, FiPhone, FiBook, FiHash } from 'react-icons/fi';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst SignUp = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    studentId: '',\n    department: '',\n    year: '',\n    phone: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const { register, loading } = useAuth();\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.name) {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.length < 2) {\n      newErrors.name = 'Name must be at least 2 characters';\n    }\n\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n\n    if (formData.year && (formData.year < 1 || formData.year > 4)) {\n      newErrors.year = 'Year must be between 1 and 4';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    const { confirmPassword, ...registrationData } = formData;\n    \n    // Convert year to number if provided\n    if (registrationData.year) {\n      registrationData.year = parseInt(registrationData.year);\n    }\n\n    const result = await register(registrationData);\n    \n    if (result.success) {\n      navigate('/dashboard');\n    }\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-wrapper\">\n        {/* Left Side - Image */}\n        <div className=\"auth-image-section\">\n          <div className=\"auth-image-overlay\">\n            <div className=\"auth-image-content\">\n              <h2 className=\"auth-image-title\">Join Our Community!</h2>\n              <p className=\"auth-image-subtitle\">\n                Start your career journey with us\n              </p>\n              <div className=\"auth-features\">\n                <div className=\"feature-item\">\n                  <span className=\"feature-icon\">🚀</span>\n                  <span>Boost Your Career</span>\n                </div>\n                <div className=\"feature-item\">\n                  <span className=\"feature-icon\">💼</span>\n                  <span>Professional CV Builder</span>\n                </div>\n                <div className=\"feature-item\">\n                  <span className=\"feature-icon\">🎓</span>\n                  <span>Skill Development</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Right Side - Registration Form */}\n        <div className=\"auth-form-section\">\n          <div className=\"auth-form-container\">\n            <div className=\"auth-header\">\n              <h1 className=\"auth-title\">Create Account</h1>\n              <p className=\"auth-subtitle\">Join our CV management system</p>\n            </div>\n\n            <form onSubmit={handleSubmit} className=\"auth-form\">\n              <div className=\"form-group\">\n                <label htmlFor=\"name\" className=\"form-label\">\n                  Full Name *\n                </label>\n                <div className=\"input-wrapper\">\n                  <FiUser className=\"input-icon\" />\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleChange}\n                    className={`form-input ${errors.name ? 'error' : ''}`}\n                    placeholder=\"Enter your full name\"\n                    disabled={loading}\n                  />\n                </div>\n                {errors.name && (\n                  <p className=\"form-error\">{errors.name}</p>\n                )}\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"email\" className=\"form-label\">\n                  Email Address *\n                </label>\n                <div className=\"input-wrapper\">\n                  <FiMail className=\"input-icon\" />\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleChange}\n                    className={`form-input ${errors.email ? 'error' : ''}`}\n                    placeholder=\"Enter your email\"\n                    disabled={loading}\n                  />\n                </div>\n                {errors.email && (\n                  <p className=\"form-error\">{errors.email}</p>\n                )}\n              </div>\n\n              <div className=\"form-row\">\n                <div className=\"form-group\">\n                  <label htmlFor=\"password\" className=\"form-label\">\n                    Password *\n                  </label>\n                  <div className=\"input-wrapper\">\n                    <FiLock className=\"input-icon\" />\n                    <input\n                      type={showPassword ? 'text' : 'password'}\n                      id=\"password\"\n                      name=\"password\"\n                      value={formData.password}\n                      onChange={handleChange}\n                      className={`form-input ${errors.password ? 'error' : ''}`}\n                      placeholder=\"Enter password\"\n                      disabled={loading}\n                    />\n                    <button\n                      type=\"button\"\n                      className=\"password-toggle\"\n                      onClick={() => setShowPassword(!showPassword)}\n                    >\n                      {showPassword ? <FiEyeOff /> : <FiEye />}\n                    </button>\n                  </div>\n                  {errors.password && (\n                    <p className=\"form-error\">{errors.password}</p>\n                  )}\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"confirmPassword\" className=\"form-label\">\n                    Confirm Password *\n                  </label>\n                  <div className=\"input-wrapper\">\n                    <FiLock className=\"input-icon\" />\n                    <input\n                      type={showConfirmPassword ? 'text' : 'password'}\n                      id=\"confirmPassword\"\n                      name=\"confirmPassword\"\n                      value={formData.confirmPassword}\n                      onChange={handleChange}\n                      className={`form-input ${errors.confirmPassword ? 'error' : ''}`}\n                      placeholder=\"Confirm password\"\n                      disabled={loading}\n                    />\n                    <button\n                      type=\"button\"\n                      className=\"password-toggle\"\n                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                    >\n                      {showConfirmPassword ? <FiEyeOff /> : <FiEye />}\n                    </button>\n                  </div>\n                  {errors.confirmPassword && (\n                    <p className=\"form-error\">{errors.confirmPassword}</p>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"form-row\">\n                <div className=\"form-group\">\n                  <label htmlFor=\"studentId\" className=\"form-label\">\n                    Student ID\n                  </label>\n                  <div className=\"input-wrapper\">\n                    <FiHash className=\"input-icon\" />\n                    <input\n                      type=\"text\"\n                      id=\"studentId\"\n                      name=\"studentId\"\n                      value={formData.studentId}\n                      onChange={handleChange}\n                      className=\"form-input\"\n                      placeholder=\"Enter student ID\"\n                      disabled={loading}\n                    />\n                  </div>\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"year\" className=\"form-label\">\n                    Year\n                  </label>\n                  <div className=\"input-wrapper\">\n                    <FiBook className=\"input-icon\" />\n                    <select\n                      id=\"year\"\n                      name=\"year\"\n                      value={formData.year}\n                      onChange={handleChange}\n                      className={`form-input ${errors.year ? 'error' : ''}`}\n                      disabled={loading}\n                    >\n                      <option value=\"\">Select year</option>\n                      <option value=\"1\">1st Year</option>\n                      <option value=\"2\">2nd Year</option>\n                      <option value=\"3\">3rd Year</option>\n                      <option value=\"4\">4th Year</option>\n                    </select>\n                  </div>\n                  {errors.year && (\n                    <p className=\"form-error\">{errors.year}</p>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"department\" className=\"form-label\">\n                  Department\n                </label>\n                <div className=\"input-wrapper\">\n                  <FiBook className=\"input-icon\" />\n                  <input\n                    type=\"text\"\n                    id=\"department\"\n                    name=\"department\"\n                    value={formData.department}\n                    onChange={handleChange}\n                    className=\"form-input\"\n                    placeholder=\"Enter your department\"\n                    disabled={loading}\n                  />\n                </div>\n              </div>\n\n              <div className=\"form-group\">\n                <label htmlFor=\"phone\" className=\"form-label\">\n                  Phone Number\n                </label>\n                <div className=\"input-wrapper\">\n                  <FiPhone className=\"input-icon\" />\n                  <input\n                    type=\"tel\"\n                    id=\"phone\"\n                    name=\"phone\"\n                    value={formData.phone}\n                    onChange={handleChange}\n                    className=\"form-input\"\n                    placeholder=\"Enter your phone number\"\n                    disabled={loading}\n                  />\n                </div>\n              </div>\n\n              <button\n                type=\"submit\"\n                className=\"btn btn-primary btn-full\"\n                disabled={loading}\n              >\n                {loading ? (\n                  <div className=\"btn-loading\">\n                    <div className=\"spinner-small\"></div>\n                    Creating Account...\n                  </div>\n                ) : (\n                  'Create Account'\n                )}\n              </button>\n            </form>\n\n            <div className=\"auth-footer\">\n              <p>\n                Already have an account?{' '}\n                <Link to=\"/signin\" className=\"auth-link\">\n                  Sign In\n                </Link>\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SignUp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,QAAQ,gBAAgB;AACjG,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM;IAAEgC,QAAQ;IAAEC;EAAQ,CAAC,GAAGtB,OAAO,CAAC,CAAC;EACvC,MAAMuB,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAE9B,MAAMiC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAElB,IAAI;MAAEmB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCrB,WAAW,CAACsB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACrB,IAAI,GAAGmB;IACV,CAAC,CAAC,CAAC;IACH;IACA,IAAIX,MAAM,CAACR,IAAI,CAAC,EAAE;MAChBS,SAAS,CAACY,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACrB,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMsB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACzB,QAAQ,CAACE,IAAI,EAAE;MAClBuB,SAAS,CAACvB,IAAI,GAAG,kBAAkB;IACrC,CAAC,MAAM,IAAIF,QAAQ,CAACE,IAAI,CAACwB,MAAM,GAAG,CAAC,EAAE;MACnCD,SAAS,CAACvB,IAAI,GAAG,oCAAoC;IACvD;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,EAAE;MACnBsB,SAAS,CAACtB,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACwB,IAAI,CAAC3B,QAAQ,CAACG,KAAK,CAAC,EAAE;MAC/CsB,SAAS,CAACtB,KAAK,GAAG,kBAAkB;IACtC;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;MACtBqB,SAAS,CAACrB,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIJ,QAAQ,CAACI,QAAQ,CAACsB,MAAM,GAAG,CAAC,EAAE;MACvCD,SAAS,CAACrB,QAAQ,GAAG,wCAAwC;IAC/D;IAEA,IAAI,CAACJ,QAAQ,CAACK,eAAe,EAAE;MAC7BoB,SAAS,CAACpB,eAAe,GAAG,8BAA8B;IAC5D,CAAC,MAAM,IAAIL,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MACzDoB,SAAS,CAACpB,eAAe,GAAG,wBAAwB;IACtD;IAEA,IAAIL,QAAQ,CAACQ,IAAI,KAAKR,QAAQ,CAACQ,IAAI,GAAG,CAAC,IAAIR,QAAQ,CAACQ,IAAI,GAAG,CAAC,CAAC,EAAE;MAC7DiB,SAAS,CAACjB,IAAI,GAAG,8BAA8B;IACjD;IAEAG,SAAS,CAACc,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACC,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOV,CAAC,IAAK;IAChCA,CAAC,CAACW,cAAc,CAAC,CAAC;IAElB,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,MAAM;MAAEnB,eAAe;MAAE,GAAG2B;IAAiB,CAAC,GAAGhC,QAAQ;;IAEzD;IACA,IAAIgC,gBAAgB,CAACxB,IAAI,EAAE;MACzBwB,gBAAgB,CAACxB,IAAI,GAAGyB,QAAQ,CAACD,gBAAgB,CAACxB,IAAI,CAAC;IACzD;IAEA,MAAM0B,MAAM,GAAG,MAAMlB,QAAQ,CAACgB,gBAAgB,CAAC;IAE/C,IAAIE,MAAM,CAACC,OAAO,EAAE;MAClBjB,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,oBACErB,OAAA;IAAKuC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BxC,OAAA;MAAKuC,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAE3BxC,OAAA;QAAKuC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjCxC,OAAA;UAAKuC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCxC,OAAA;YAAKuC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCxC,OAAA;cAAIuC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzD5C,OAAA;cAAGuC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAEnC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ5C,OAAA;cAAKuC,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BxC,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BxC,OAAA;kBAAMuC,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC5C,OAAA;kBAAAwC,QAAA,EAAM;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BxC,OAAA;kBAAMuC,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC5C,OAAA;kBAAAwC,QAAA,EAAM;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BxC,OAAA;kBAAMuC,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxC5C,OAAA;kBAAAwC,QAAA,EAAM;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5C,OAAA;QAAKuC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCxC,OAAA;UAAKuC,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCxC,OAAA;YAAKuC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BxC,OAAA;cAAIuC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9C5C,OAAA;cAAGuC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eAEN5C,OAAA;YAAM6C,QAAQ,EAAEZ,YAAa;YAACM,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjDxC,OAAA;cAAKuC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBxC,OAAA;gBAAO8C,OAAO,EAAC,MAAM;gBAACP,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAE7C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5C,OAAA;gBAAKuC,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BxC,OAAA,CAACV,MAAM;kBAACiD,SAAS,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjC5C,OAAA;kBACE+C,IAAI,EAAC,MAAM;kBACXC,EAAE,EAAC,MAAM;kBACT3C,IAAI,EAAC,MAAM;kBACXmB,KAAK,EAAErB,QAAQ,CAACE,IAAK;kBACrB4C,QAAQ,EAAE3B,YAAa;kBACvBiB,SAAS,EAAE,cAAc1B,MAAM,CAACR,IAAI,GAAG,OAAO,GAAG,EAAE,EAAG;kBACtD6C,WAAW,EAAC,sBAAsB;kBAClCC,QAAQ,EAAE/B;gBAAQ;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EACL/B,MAAM,CAACR,IAAI,iBACVL,OAAA;gBAAGuC,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE3B,MAAM,CAACR;cAAI;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN5C,OAAA;cAAKuC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBxC,OAAA;gBAAO8C,OAAO,EAAC,OAAO;gBAACP,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAE9C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5C,OAAA;gBAAKuC,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BxC,OAAA,CAACT,MAAM;kBAACgD,SAAS,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjC5C,OAAA;kBACE+C,IAAI,EAAC,OAAO;kBACZC,EAAE,EAAC,OAAO;kBACV3C,IAAI,EAAC,OAAO;kBACZmB,KAAK,EAAErB,QAAQ,CAACG,KAAM;kBACtB2C,QAAQ,EAAE3B,YAAa;kBACvBiB,SAAS,EAAE,cAAc1B,MAAM,CAACP,KAAK,GAAG,OAAO,GAAG,EAAE,EAAG;kBACvD4C,WAAW,EAAC,kBAAkB;kBAC9BC,QAAQ,EAAE/B;gBAAQ;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EACL/B,MAAM,CAACP,KAAK,iBACXN,OAAA;gBAAGuC,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE3B,MAAM,CAACP;cAAK;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN5C,OAAA;cAAKuC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBxC,OAAA;gBAAKuC,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBxC,OAAA;kBAAO8C,OAAO,EAAC,UAAU;kBAACP,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAEjD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR5C,OAAA;kBAAKuC,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BxC,OAAA,CAACR,MAAM;oBAAC+C,SAAS,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjC5C,OAAA;oBACE+C,IAAI,EAAEhC,YAAY,GAAG,MAAM,GAAG,UAAW;oBACzCiC,EAAE,EAAC,UAAU;oBACb3C,IAAI,EAAC,UAAU;oBACfmB,KAAK,EAAErB,QAAQ,CAACI,QAAS;oBACzB0C,QAAQ,EAAE3B,YAAa;oBACvBiB,SAAS,EAAE,cAAc1B,MAAM,CAACN,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;oBAC1D2C,WAAW,EAAC,gBAAgB;oBAC5BC,QAAQ,EAAE/B;kBAAQ;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACF5C,OAAA;oBACE+C,IAAI,EAAC,QAAQ;oBACbR,SAAS,EAAC,iBAAiB;oBAC3Ba,OAAO,EAAEA,CAAA,KAAMpC,eAAe,CAAC,CAACD,YAAY,CAAE;oBAAAyB,QAAA,EAE7CzB,YAAY,gBAAGf,OAAA,CAACN,QAAQ;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAG5C,OAAA,CAACP,KAAK;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EACL/B,MAAM,CAACN,QAAQ,iBACdP,OAAA;kBAAGuC,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE3B,MAAM,CAACN;gBAAQ;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC/C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN5C,OAAA;gBAAKuC,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBxC,OAAA;kBAAO8C,OAAO,EAAC,iBAAiB;kBAACP,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAExD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR5C,OAAA;kBAAKuC,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BxC,OAAA,CAACR,MAAM;oBAAC+C,SAAS,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjC5C,OAAA;oBACE+C,IAAI,EAAE9B,mBAAmB,GAAG,MAAM,GAAG,UAAW;oBAChD+B,EAAE,EAAC,iBAAiB;oBACpB3C,IAAI,EAAC,iBAAiB;oBACtBmB,KAAK,EAAErB,QAAQ,CAACK,eAAgB;oBAChCyC,QAAQ,EAAE3B,YAAa;oBACvBiB,SAAS,EAAE,cAAc1B,MAAM,CAACL,eAAe,GAAG,OAAO,GAAG,EAAE,EAAG;oBACjE0C,WAAW,EAAC,kBAAkB;oBAC9BC,QAAQ,EAAE/B;kBAAQ;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACF5C,OAAA;oBACE+C,IAAI,EAAC,QAAQ;oBACbR,SAAS,EAAC,iBAAiB;oBAC3Ba,OAAO,EAAEA,CAAA,KAAMlC,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;oBAAAuB,QAAA,EAE3DvB,mBAAmB,gBAAGjB,OAAA,CAACN,QAAQ;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAG5C,OAAA,CAACP,KAAK;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EACL/B,MAAM,CAACL,eAAe,iBACrBR,OAAA;kBAAGuC,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE3B,MAAM,CAACL;gBAAe;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CACtD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5C,OAAA;cAAKuC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBxC,OAAA;gBAAKuC,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBxC,OAAA;kBAAO8C,OAAO,EAAC,WAAW;kBAACP,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAElD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR5C,OAAA;kBAAKuC,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BxC,OAAA,CAACH,MAAM;oBAAC0C,SAAS,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjC5C,OAAA;oBACE+C,IAAI,EAAC,MAAM;oBACXC,EAAE,EAAC,WAAW;oBACd3C,IAAI,EAAC,WAAW;oBAChBmB,KAAK,EAAErB,QAAQ,CAACM,SAAU;oBAC1BwC,QAAQ,EAAE3B,YAAa;oBACvBiB,SAAS,EAAC,YAAY;oBACtBW,WAAW,EAAC,kBAAkB;oBAC9BC,QAAQ,EAAE/B;kBAAQ;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5C,OAAA;gBAAKuC,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBxC,OAAA;kBAAO8C,OAAO,EAAC,MAAM;kBAACP,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAE7C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR5C,OAAA;kBAAKuC,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BxC,OAAA,CAACJ,MAAM;oBAAC2C,SAAS,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjC5C,OAAA;oBACEgD,EAAE,EAAC,MAAM;oBACT3C,IAAI,EAAC,MAAM;oBACXmB,KAAK,EAAErB,QAAQ,CAACQ,IAAK;oBACrBsC,QAAQ,EAAE3B,YAAa;oBACvBiB,SAAS,EAAE,cAAc1B,MAAM,CAACF,IAAI,GAAG,OAAO,GAAG,EAAE,EAAG;oBACtDwC,QAAQ,EAAE/B,OAAQ;oBAAAoB,QAAA,gBAElBxC,OAAA;sBAAQwB,KAAK,EAAC,EAAE;sBAAAgB,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACrC5C,OAAA;sBAAQwB,KAAK,EAAC,GAAG;sBAAAgB,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACnC5C,OAAA;sBAAQwB,KAAK,EAAC,GAAG;sBAAAgB,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACnC5C,OAAA;sBAAQwB,KAAK,EAAC,GAAG;sBAAAgB,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACnC5C,OAAA;sBAAQwB,KAAK,EAAC,GAAG;sBAAAgB,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EACL/B,MAAM,CAACF,IAAI,iBACVX,OAAA;kBAAGuC,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE3B,MAAM,CAACF;gBAAI;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5C,OAAA;cAAKuC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBxC,OAAA;gBAAO8C,OAAO,EAAC,YAAY;gBAACP,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAEnD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5C,OAAA;gBAAKuC,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BxC,OAAA,CAACJ,MAAM;kBAAC2C,SAAS,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjC5C,OAAA;kBACE+C,IAAI,EAAC,MAAM;kBACXC,EAAE,EAAC,YAAY;kBACf3C,IAAI,EAAC,YAAY;kBACjBmB,KAAK,EAAErB,QAAQ,CAACO,UAAW;kBAC3BuC,QAAQ,EAAE3B,YAAa;kBACvBiB,SAAS,EAAC,YAAY;kBACtBW,WAAW,EAAC,uBAAuB;kBACnCC,QAAQ,EAAE/B;gBAAQ;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5C,OAAA;cAAKuC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBxC,OAAA;gBAAO8C,OAAO,EAAC,OAAO;gBAACP,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAE9C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR5C,OAAA;gBAAKuC,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BxC,OAAA,CAACL,OAAO;kBAAC4C,SAAS,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClC5C,OAAA;kBACE+C,IAAI,EAAC,KAAK;kBACVC,EAAE,EAAC,OAAO;kBACV3C,IAAI,EAAC,OAAO;kBACZmB,KAAK,EAAErB,QAAQ,CAACS,KAAM;kBACtBqC,QAAQ,EAAE3B,YAAa;kBACvBiB,SAAS,EAAC,YAAY;kBACtBW,WAAW,EAAC,yBAAyB;kBACrCC,QAAQ,EAAE/B;gBAAQ;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5C,OAAA;cACE+C,IAAI,EAAC,QAAQ;cACbR,SAAS,EAAC,0BAA0B;cACpCY,QAAQ,EAAE/B,OAAQ;cAAAoB,QAAA,EAEjBpB,OAAO,gBACNpB,OAAA;gBAAKuC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BxC,OAAA;kBAAKuC,SAAS,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,uBAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAEN;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEP5C,OAAA;YAAKuC,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BxC,OAAA;cAAAwC,QAAA,GAAG,0BACuB,EAAC,GAAG,eAC5BxC,OAAA,CAACZ,IAAI;gBAACiE,EAAE,EAAC,SAAS;gBAACd,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAEzC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAxVID,MAAM;EAAA,QAcoBH,OAAO,EACpBT,WAAW;AAAA;AAAAiE,EAAA,GAfxBrD,MAAM;AA0VZ,eAAeA,MAAM;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}