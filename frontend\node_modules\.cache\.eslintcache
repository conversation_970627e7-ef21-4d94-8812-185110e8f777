[{"C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\Footer.js": "4", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\ProtectedRoute.js": "5", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\SignIn.js": "6", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\LoadingSpinner.js": "7", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\News.js": "8", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\CVUpload.js": "9", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\Header.js": "10", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\SignUp.js": "11", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\Dashboard.js": "12", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\Profile.js": "13", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\NewsDetail.js": "14", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\NewsSection.js": "15", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\ImageSlideshow.js": "16", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\Courses.js": "17", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\Sessions.js": "18"}, {"size": 350, "mtime": 1754390987190, "results": "19", "hashOfConfig": "20"}, {"size": 3784, "mtime": 1756019168791, "results": "21", "hashOfConfig": "20"}, {"size": 6939, "mtime": 1754391105000, "results": "22", "hashOfConfig": "20"}, {"size": 315, "mtime": 1754391228628, "results": "23", "hashOfConfig": "20"}, {"size": 427, "mtime": 1754391122902, "results": "24", "hashOfConfig": "20"}, {"size": 5720, "mtime": 1756018723565, "results": "25", "hashOfConfig": "20"}, {"size": 423, "mtime": 1754391130692, "results": "26", "hashOfConfig": "20"}, {"size": 10231, "mtime": 1754391470906, "results": "27", "hashOfConfig": "20"}, {"size": 11162, "mtime": 1754391390048, "results": "28", "hashOfConfig": "20"}, {"size": 4850, "mtime": 1756019009076, "results": "29", "hashOfConfig": "20"}, {"size": 12295, "mtime": 1756018919667, "results": "30", "hashOfConfig": "20"}, {"size": 10293, "mtime": 1756018983832, "results": "31", "hashOfConfig": "20"}, {"size": 10212, "mtime": 1754391431369, "results": "32", "hashOfConfig": "20"}, {"size": 9163, "mtime": 1754391510245, "results": "33", "hashOfConfig": "20"}, {"size": 6941, "mtime": 1754391333213, "results": "34", "hashOfConfig": "20"}, {"size": 7239, "mtime": 1754391299982, "results": "35", "hashOfConfig": "20"}, {"size": 9353, "mtime": 1756019103150, "results": "36", "hashOfConfig": "20"}, {"size": 10686, "mtime": 1756019146852, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jk3h4a", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\contexts\\AuthContext.js", ["92"], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\SignIn.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\News.js", ["93"], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\CVUpload.js", ["94", "95", "96"], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\Header.js", ["97"], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\SignUp.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\NewsDetail.js", ["98"], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\NewsSection.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\ImageSlideshow.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\Courses.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\Sessions.js", [], [], {"ruleId": "99", "severity": 1, "message": "100", "line": 149, "column": 6, "nodeType": "101", "endLine": 149, "endColumn": 8, "suggestions": "102"}, {"ruleId": "99", "severity": 1, "message": "103", "line": 17, "column": 6, "nodeType": "101", "endLine": 17, "endColumn": 49, "suggestions": "104"}, {"ruleId": "105", "severity": 1, "message": "106", "line": 2, "column": 50, "nodeType": "107", "messageId": "108", "endLine": 2, "endColumn": 55}, {"ruleId": "105", "severity": 1, "message": "109", "line": 48, "column": 13, "nodeType": "107", "messageId": "108", "endLine": 48, "endColumn": 21}, {"ruleId": "105", "severity": 1, "message": "110", "line": 72, "column": 9, "nodeType": "107", "messageId": "108", "endLine": 72, "endColumn": 25}, {"ruleId": "105", "severity": 1, "message": "111", "line": 3, "column": 41, "nodeType": "107", "messageId": "108", "endLine": 3, "endColumn": 51}, {"ruleId": "99", "severity": 1, "message": "112", "line": 18, "column": 6, "nodeType": "101", "endLine": 18, "endColumn": 10, "suggestions": "113"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'state.token'. Either include it or remove the dependency array.", "ArrayExpression", ["114"], "React Hook useEffect has a missing dependency: 'fetchNews'. Either include it or remove the dependency array.", ["115"], "no-unused-vars", "'FiEye' is defined but never used.", "Identifier", "unusedVar", "'response' is assigned a value but never used.", "'handleFileSelect' is assigned a value but never used.", "'FiSettings' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchArticle'. Either include it or remove the dependency array.", ["116"], {"desc": "117", "fix": "118"}, {"desc": "119", "fix": "120"}, {"desc": "121", "fix": "122"}, "Update the dependencies array to be: [state.token]", {"range": "123", "text": "124"}, "Update the dependencies array to be: [currentPage, fetchNews, searchTerm, selectedCategory]", {"range": "125", "text": "126"}, "Update the dependencies array to be: [fetchArticle, id]", {"range": "127", "text": "128"}, [3601, 3603], "[state.token]", [624, 667], "[currentPage, fetchNews, searchTerm, selectedCategory]", [647, 651], "[fetchArticle, id]"]