[{"C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\Footer.js": "4", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\ProtectedRoute.js": "5", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\SignIn.js": "6", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\LoadingSpinner.js": "7", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\News.js": "8", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\CVUpload.js": "9", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\Header.js": "10", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\SignUp.js": "11", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\Dashboard.js": "12", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\Profile.js": "13", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\NewsDetail.js": "14", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\NewsSection.js": "15", "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\ImageSlideshow.js": "16"}, {"size": 350, "mtime": 1754390987190, "results": "17", "hashOfConfig": "18"}, {"size": 3303, "mtime": 1754391001788, "results": "19", "hashOfConfig": "18"}, {"size": 6939, "mtime": 1754391105000, "results": "20", "hashOfConfig": "18"}, {"size": 315, "mtime": 1754391228628, "results": "21", "hashOfConfig": "18"}, {"size": 427, "mtime": 1754391122902, "results": "22", "hashOfConfig": "18"}, {"size": 3416, "mtime": 1754391152889, "results": "23", "hashOfConfig": "18"}, {"size": 423, "mtime": 1754391130692, "results": "24", "hashOfConfig": "18"}, {"size": 10231, "mtime": 1754391470906, "results": "25", "hashOfConfig": "18"}, {"size": 11162, "mtime": 1754391390048, "results": "26", "hashOfConfig": "18"}, {"size": 4772, "mtime": 1754391213972, "results": "27", "hashOfConfig": "18"}, {"size": 8244, "mtime": 1754391182167, "results": "28", "hashOfConfig": "18"}, {"size": 8761, "mtime": 1754391264247, "results": "29", "hashOfConfig": "18"}, {"size": 10212, "mtime": 1754391431369, "results": "30", "hashOfConfig": "18"}, {"size": 9163, "mtime": 1754391510245, "results": "31", "hashOfConfig": "18"}, {"size": 6941, "mtime": 1754391333213, "results": "32", "hashOfConfig": "18"}, {"size": 7239, "mtime": 1754391299982, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jk3h4a", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\contexts\\AuthContext.js", ["82"], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\SignIn.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\News.js", ["83"], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\CVUpload.js", ["84", "85", "86"], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\Header.js", ["87"], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\SignUp.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\pages\\NewsDetail.js", ["88"], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\NewsSection.js", [], [], "C:\\Users\\<USER>\\Documents\\OutSource\\CV\\frontend\\src\\components\\ImageSlideshow.js", [], [], {"ruleId": "89", "severity": 1, "message": "90", "line": 149, "column": 6, "nodeType": "91", "endLine": 149, "endColumn": 8, "suggestions": "92"}, {"ruleId": "89", "severity": 1, "message": "93", "line": 17, "column": 6, "nodeType": "91", "endLine": 17, "endColumn": 49, "suggestions": "94"}, {"ruleId": "95", "severity": 1, "message": "96", "line": 2, "column": 50, "nodeType": "97", "messageId": "98", "endLine": 2, "endColumn": 55}, {"ruleId": "95", "severity": 1, "message": "99", "line": 48, "column": 13, "nodeType": "97", "messageId": "98", "endLine": 48, "endColumn": 21}, {"ruleId": "95", "severity": 1, "message": "100", "line": 72, "column": 9, "nodeType": "97", "messageId": "98", "endLine": 72, "endColumn": 25}, {"ruleId": "95", "severity": 1, "message": "101", "line": 3, "column": 41, "nodeType": "97", "messageId": "98", "endLine": 3, "endColumn": 51}, {"ruleId": "89", "severity": 1, "message": "102", "line": 18, "column": 6, "nodeType": "91", "endLine": 18, "endColumn": 10, "suggestions": "103"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'state.token'. Either include it or remove the dependency array.", "ArrayExpression", ["104"], "React Hook useEffect has a missing dependency: 'fetchNews'. Either include it or remove the dependency array.", ["105"], "no-unused-vars", "'FiEye' is defined but never used.", "Identifier", "unusedVar", "'response' is assigned a value but never used.", "'handleFileSelect' is assigned a value but never used.", "'FiSettings' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchArticle'. Either include it or remove the dependency array.", ["106"], {"desc": "107", "fix": "108"}, {"desc": "109", "fix": "110"}, {"desc": "111", "fix": "112"}, "Update the dependencies array to be: [state.token]", {"range": "113", "text": "114"}, "Update the dependencies array to be: [currentPage, fetchNews, searchTerm, selectedCategory]", {"range": "115", "text": "116"}, "Update the dependencies array to be: [fetchArticle, id]", {"range": "117", "text": "118"}, [3601, 3603], "[state.token]", [624, 667], "[currentPage, fetchNews, searchTerm, selectedCategory]", [647, 651], "[fetchArticle, id]"]