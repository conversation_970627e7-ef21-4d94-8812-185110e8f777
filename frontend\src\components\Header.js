import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { FiMenu, FiX, FiUser, FiLogOut, FiSettings, FiUpload, FiBook, FiCalendar } from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/signin');
  };

  const isActiveLink = (path) => {
    return location.pathname === path;
  };

  const getUserInitials = (name) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const navLinks = [
    { path: '/upload-cv', label: 'Upload CV', icon: FiUpload },
    { path: '/courses', label: 'Courses & Webinars', icon: FiBook },
    { path: '/sessions', label: 'University Sessions', icon: FiCalendar }
  ];

  return (
    <header className="header">
      <div className="header-content">
        {/* Logo */}
        <Link to="/dashboard" className="logo">
          CV Manager
        </Link>

        {/* Desktop Navigation */}
        <nav className="nav-menu">
          {navLinks.map((link) => (
            <Link
              key={link.path}
              to={link.path}
              className={`nav-link ${isActiveLink(link.path) ? 'active' : ''}`}
            >
              {link.icon && <link.icon style={{ marginRight: '0.5rem' }} />}
              {link.label}
            </Link>
          ))}
        </nav>

        {/* User Menu */}
        <div className="user-menu">
          <button
            className="user-menu-button"
            onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
          >
            <div className="user-avatar">
              {user?.profileImage ? (
                <img
                  src={user.profileImage}
                  alt={user.name}
                  className="user-avatar"
                />
              ) : (
                getUserInitials(user?.name || 'U')
              )}
            </div>
            <span className="user-name">{user?.name}</span>
          </button>

          {isUserMenuOpen && (
            <div className="user-menu-dropdown">
              <Link
                to="/profile"
                className="user-menu-item"
                onClick={() => setIsUserMenuOpen(false)}
              >
                <FiUser style={{ marginRight: '0.5rem' }} />
                Profile
              </Link>
              <button
                className="user-menu-item"
                onClick={handleLogout}
              >
                <FiLogOut style={{ marginRight: '0.5rem' }} />
                Logout
              </button>
            </div>
          )}
        </div>

        {/* Mobile Menu Button */}
        <button
          className="mobile-menu-button"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          {isMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}
        </button>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="nav-menu mobile-open">
            {navLinks.map((link) => (
              <Link
                key={link.path}
                to={link.path}
                className={`nav-link ${isActiveLink(link.path) ? 'active' : ''}`}
                onClick={() => setIsMenuOpen(false)}
              >
                {link.icon && <link.icon style={{ marginRight: '0.5rem' }} />}
                {link.label}
              </Link>
            ))}
            <div style={{ borderTop: '1px solid #e5e7eb', paddingTop: '1rem', marginTop: '1rem' }}>
              <Link
                to="/profile"
                className="nav-link"
                onClick={() => setIsMenuOpen(false)}
              >
                <FiUser style={{ marginRight: '0.5rem' }} />
                Profile
              </Link>
              <button
                className="nav-link"
                onClick={() => {
                  setIsMenuOpen(false);
                  handleLogout();
                }}
                style={{ width: '100%', textAlign: 'left' }}
              >
                <FiLogOut style={{ marginRight: '0.5rem' }} />
                Logout
              </button>
            </div>
          </nav>
        )}
      </div>

      {/* Overlay for mobile menu */}
      {(isMenuOpen || isUserMenuOpen) && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => {
            setIsMenuOpen(false);
            setIsUserMenuOpen(false);
          }}
        />
      )}
    </header>
  );
};

export default Header;
