import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FiUsers, FiFileText, FiTrendingUp, FiUpload, FiEye, FiCalendar } from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';
import ImageSlideshow from '../components/ImageSlideshow';
import NewsSection from '../components/NewsSection';
import axios from 'axios';

const Dashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const response = await axios.get('/api/users/dashboard-stats');
      setStats(response.data.data);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const getWelcomeMessage = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  if (loading) {
    return <LoadingSpinner text="Loading dashboard..." />;
  }

  return (
    <div className="page-container">
      {/* Welcome Section */}
      <div className="page-header">
        <h1 className="page-title">
          {getWelcomeMessage()}, {user?.name}!
        </h1>
        <p className="page-subtitle">
          Welcome to your CV management dashboard
        </p>
      </div>

      {/* Image Slideshow */}
      <div className="mb-5">
        <ImageSlideshow />
      </div>

      {/* Motivational Cards */}
      <div className="motivational-section mb-5">
        <h2 className="section-title">Stay Motivated</h2>
        <div className="motivational-grid">
          <div className="motivational-card">
            <div className="motivational-icon">🎯</div>
            <h3 className="motivational-title">Find Your Dream Job</h3>
            <p className="motivational-text">
              "Success is not final, failure is not fatal: it is the courage to continue that counts."
            </p>
            <div className="motivational-author">- Winston Churchill</div>
          </div>

          <div className="motivational-card">
            <div className="motivational-icon">📈</div>
            <h3 className="motivational-title">Upgrade Your Skills</h3>
            <p className="motivational-text">
              "The only way to do great work is to love what you do. If you haven't found it yet, keep looking."
            </p>
            <div className="motivational-author">- Steve Jobs</div>
          </div>

          <div className="motivational-card">
            <div className="motivational-icon">🚀</div>
            <h3 className="motivational-title">Boost Your Career</h3>
            <p className="motivational-text">
              "Your limitation—it's only your imagination. Push yourself, because no one else is going to do it for you."
            </p>
            <div className="motivational-author">- Anonymous</div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="dashboard-grid mb-5">
        {user?.role === 'admin' ? (
          // Admin Stats
          <>
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="stat-number">{stats?.totalUsers || 0}</p>
                  <p className="stat-label">Total Users</p>
                </div>
                <FiUsers className="text-3xl text-blue-500" />
              </div>
            </div>
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="stat-number">{stats?.totalStudents || 0}</p>
                  <p className="stat-label">Students</p>
                </div>
                <FiUsers className="text-3xl text-green-500" />
              </div>
            </div>
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="stat-number">{stats?.totalCVs || 0}</p>
                  <p className="stat-label">Total CVs</p>
                </div>
                <FiFileText className="text-3xl text-purple-500" />
              </div>
            </div>
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="stat-number">{stats?.totalNews || 0}</p>
                  <p className="stat-label">News Articles</p>
                </div>
                <FiTrendingUp className="text-3xl text-orange-500" />
              </div>
            </div>
          </>
        ) : (
          // Student Stats
          <>
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="stat-number">{stats?.myCVs || 0}</p>
                  <p className="stat-label">My CVs</p>
                </div>
                <FiFileText className="text-3xl text-blue-500" />
              </div>
            </div>
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="stat-number">{stats?.totalDownloads || 0}</p>
                  <p className="stat-label">Total Downloads</p>
                </div>
                <FiEye className="text-3xl text-green-500" />
              </div>
            </div>
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="stat-number">
                    {stats?.latestCV ? 'Yes' : 'No'}
                  </p>
                  <p className="stat-label">CV Uploaded</p>
                </div>
                <FiUpload className="text-3xl text-purple-500" />
              </div>
            </div>
            <div className="stat-card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="stat-number">
                    {stats?.latestCV 
                      ? new Date(stats.latestCV.createdAt).toLocaleDateString()
                      : 'N/A'
                    }
                  </p>
                  <p className="stat-label">Last Upload</p>
                </div>
                <FiCalendar className="text-3xl text-orange-500" />
              </div>
            </div>
          </>
        )}
      </div>

      {/* Quick Actions */}
      <div className="card mb-5">
        <div className="card-header">
          <h2 className="text-xl font-semibold">Quick Actions</h2>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-2 gap-4">
            <Link to="/upload-cv" className="btn btn-primary">
              <FiUpload style={{ marginRight: '0.5rem' }} />
              Upload CV
            </Link>
            <Link to="/news" className="btn btn-outline">
              <FiTrendingUp style={{ marginRight: '0.5rem' }} />
              View News
            </Link>
            {user?.role === 'student' && stats?.latestCV && (
              <a
                href={`/api/cv/download/${stats.latestCV.id}`}
                className="btn btn-secondary"
                target="_blank"
                rel="noopener noreferrer"
              >
                <FiFileText style={{ marginRight: '0.5rem' }} />
                Download Latest CV
              </a>
            )}
            <Link to="/profile" className="btn btn-secondary">
              <FiUsers style={{ marginRight: '0.5rem' }} />
              Edit Profile
            </Link>
          </div>
        </div>
      </div>

      {/* Recent News Section */}
      <NewsSection />

      {/* Recent Activity (Admin Only) */}
      {user?.role === 'admin' && stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Recent Users */}
          {stats.recentUsers && stats.recentUsers.length > 0 && (
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold">Recent Users</h3>
              </div>
              <div className="card-body">
                <div className="space-y-3">
                  {stats.recentUsers.map((user) => (
                    <div key={user._id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{user.name}</p>
                        <p className="text-sm text-gray-500">{user.email}</p>
                      </div>
                      <p className="text-sm text-gray-400">
                        {new Date(user.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Recent CVs */}
          {stats.recentCVs && stats.recentCVs.length > 0 && (
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold">Recent CV Uploads</h3>
              </div>
              <div className="card-body">
                <div className="space-y-3">
                  {stats.recentCVs.map((cv) => (
                    <div key={cv._id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{cv.originalName}</p>
                        <p className="text-sm text-gray-500">
                          by {cv.user?.name}
                        </p>
                      </div>
                      <p className="text-sm text-gray-400">
                        {new Date(cv.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Dashboard;
